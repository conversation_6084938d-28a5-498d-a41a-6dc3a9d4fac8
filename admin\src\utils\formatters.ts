export const formatCurrency = (amount: number): string => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(amount);
};

export const formatCryptoAmount = (amount: number, currency: string): string => {
  return `${amount.toLocaleString('en-US', { maximumFractionDigits: 8 })} ${currency}`;
};

export const formatPercentage = (value: number): string => {
  return `${value > 0 ? '+' : ''}${value.toFixed(2)}%`;
};

export const formatTime = (time: string): string => {
  return time;
};

export const formatUserRole = (role: string): string => {
  return role;
};

export const formatTransactionId = (id: string): string => {
  return id;
};

export const formatTransactionTime = (time: string): string => {
  return time;
};

export const formatUSDTAmount = (amount: number): string => {
  return `${amount.toFixed(2)} USDT`;
};

export const formatSOLAmount = (amount: number): string => {
  return `${amount.toFixed(8)} SOL`;
};

export const formatBTCAmount = (amount: number): string => {
  return `${amount.toFixed(3)} BTC`;
};

export const formatETHAmount = (amount: number): string => {
  return `${amount.toFixed(3)} ETH`;
};

export const formatServerResponseTime = (time: number): string => {
  return `${time.toFixed(1)} ms`;
};

export const formatPresencePercentage = (percentage: number): string => {
  return `${percentage}%`;
};

export const formatChatTime = (timestamp: Date): string => {
  const now = new Date();
  const diff = now.getTime() - timestamp.getTime();
  const hours = Math.floor(diff / (1000 * 60 * 60));
  
  if (hours < 24) {
    return timestamp.toLocaleTimeString('en-US', { 
      hour: '2-digit', 
      minute: '2-digit',
      hour12: false 
    });
  } else {
    return timestamp.toLocaleDateString('en-US', { 
      month: 'short', 
      day: 'numeric', 
      year: 'numeric' 
    });
  }
};