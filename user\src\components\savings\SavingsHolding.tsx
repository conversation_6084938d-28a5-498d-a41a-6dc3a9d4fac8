"use client";

import React from 'react';
import { Card } from '@/components/ui/card';
import { ArrowRight } from 'lucide-react';
import { SavingsHolding as SavingsHoldingType } from '../../types/schema';
import { formatCryptoSavings } from '../../utils/formatters';
import { CryptoIcon } from '../general';

interface SavingsHoldingProps {
  holding: SavingsHoldingType;
}

// Helper function to convert SavingsCryptoCurrency enum to symbol
const getCryptoSymbol = (currency: string) => {
  switch (currency) {
    case 'Bitcoin':
      return 'BTC';
    case 'Ethereum':
      return 'ETH';
    case 'BNB':
      return 'BNB';
    case 'Solana':
      return 'SOL';
    case 'DASH':
      return 'DASH';
    case 'Polkadot':
      return 'DOT';
    case 'Monero':
      return 'XMR';
    default:
      return 'BTC';
  }
};

export const SavingsHolding: React.FC<SavingsHoldingProps> = ({ holding }) => {
  const cryptoSymbol = getCryptoSymbol(holding.currency);
  
  return (
    <Card className="p-4 hover:bg-gray-50 transition-colors cursor-pointer rounded-3xl">
      <div className="flex items-center justify-between h-18">
        <div className="flex items-center gap-4">
          <CryptoIcon 
            symbol={cryptoSymbol}
            size={32}
            className="w-12 h-12"
          />
          <div>
            <div className="font-semibold text-lg">{holding.name}</div>
            <div className="text-sm text-muted-foreground">
              Saved: <span className='font-bold'>{formatCryptoSavings(holding.savedAmount, holding.symbol)}</span>
            </div>
          </div>
        </div>
        
        <ArrowRight className="w-5 h-5 text-muted-foreground" />
      </div>
    </Card>
  );
};