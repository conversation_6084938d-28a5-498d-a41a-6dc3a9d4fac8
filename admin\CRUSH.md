# CRUSH.md - Repository Guidelines

## Build, <PERSON><PERSON>, Test Commands
- `bun run dev` - Start development server
- `bun run build` - Build for production
- `bun run lint` - Run ESLint checks
- `bun run typecheck` - Run TypeScript type checking
- `bun test` - Run all tests

## Code Style Guidelines

### Imports
- Group imports by type (external, internal, styles)
- Alphabetize imports within groups
- Use type imports syntax for Typescript (`import type { ... }`)

### Formatting
- 2-space indentation
- Semicolons required
- Single quotes for strings
- Arrow functions for React components
- Tailwind CSS utility classes preferred over custom CSS

### Types
- TypeScript strict mode enabled
- Inline types for simple props
- Interfaces for complex types
- No implicit `any` types

### Naming
- PascalCase for components
- camelCase for functions/variables
- ALL_CAPS for constants
- `use` prefix for hooks

### Error Handling
- Use Error boundaries for React
- Type-safe error handling with `try/catch`

---

Update and extend these guidelines as needed.