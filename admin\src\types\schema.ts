import { TimePeriod, CryptoCurrency, AdministratorStatus } from './enums';

// Props types (data passed to components)
export interface DashboardProps {
  estimatedProfits: number;
  selectedTimePeriod: TimePeriod;
  user: UserProfile;
  profitsChartData: ChartDataPoint[];
  transactionsData: TransactionDataPoint[];
  collateralsData: CollateralsData;
  topRequestedCoins: TopCoin[];
}

export interface UserProfile {
  name: string;
  role: string;
  avatar: string;
  id: string;
}

export interface ChartDataPoint {
  time: string;
  value: number;
}

export interface TransactionDataPoint {
  time: string;
  Loans: number;
  Sending: number;
  Buying: number;
  Savings: number;
  Receives: number;
  Sellings: number;
}

export interface CollateralsData {
  estimatedValue: number;
  holdings: CryptoHolding[];
}

export interface CryptoHolding {
  rank: number;
  currency: CryptoCurrency;
  symbol: string;
  amount: number;
  icon: string;
}

export interface TopCoin {
  currency: CryptoCurrency;
  symbol: string;
  change: number;
  trend: 'up' | 'down';
}

// Props types for administrators page
export interface AdministratorsPageProps {
  administrators: Administrator[];
  chatMessages: ChatMessage[];
}

export interface Administrator {
  id: string;
  name: string;
  presence: number;
  status: AdministratorStatus;
  isCurrentUser: boolean;
  avatar: string;
}

export interface ChatMessage {
  id: string;
  sender: string;
  message: string;
  timestamp: Date;
  avatar: string;
}