"use client";

import {
  Table,
  TableBody,
  Table<PERSON>ell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Checkbox } from "@/components/ui/checkbox";
import { formatCryptoAmount } from "@/utils/formatters";

interface ExchangesTransaction {
  id: string;
  time: string;
  from: string;
  to: string;
  amount: number;
  currency: string;
  isHeld: boolean;
}

interface ExchangesTableProps {
  transactions: ExchangesTransaction[];
  selectedTransactions: string[];
  onTransactionSelect: (id: string, checked: boolean) => void;
}

export function ExchangesTable({ 
  transactions, 
  selectedTransactions, 
  onTransactionSelect 
}: ExchangesTableProps) {
  const getAmountColor = (currency: string) => {
    switch (currency) {
      case 'BTC':
        return 'text-orange-600';
      case 'USDT':
        return 'text-orange-600';
      default:
        return 'text-foreground';
    }
  };

  return (
    <div className="w-full">
      <Table>
        <TableHeader>
          <TableRow className="border-b border-border">
            <TableHead className="text-muted-foreground font-medium">Time</TableHead>
            <TableHead className="text-muted-foreground font-medium">ID</TableHead>
            <TableHead className="text-muted-foreground font-medium">From</TableHead>
            <TableHead className="text-muted-foreground font-medium">To</TableHead>
            <TableHead className="text-muted-foreground font-medium">Amount</TableHead>
            <TableHead className="text-muted-foreground font-medium">Hold</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {transactions.map((transaction, index) => (
            <TableRow key={`${transaction.id}-${index}`}>
              <TableCell className="font-medium">{transaction.time}</TableCell>
              <TableCell>{transaction.id}</TableCell>
              <TableCell>{transaction.from}</TableCell>
              <TableCell>{transaction.to}</TableCell>
              <TableCell className={getAmountColor(transaction.currency)}>
                {formatCryptoAmount(transaction.amount, transaction.currency)}
              </TableCell>
              <TableCell>
                <Checkbox
                  checked={selectedTransactions.includes(`${transaction.id}-${index}`)}
                  onCheckedChange={(checked) => 
                    onTransactionSelect(`${transaction.id}-${index}`, !!checked)
                  }
                />
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
}