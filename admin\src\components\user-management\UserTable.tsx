"use client";

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { ArrowUpDown } from "lucide-react";
import { TierBadge } from "./TierBadge";
import { UserTier } from "@/app/userManagementMockData";
import { useState } from "react";

interface User {
  id: string;
  email: string;
  balance: number;
  tier: UserTier;
  loan: number;
  isActive: boolean;
}

interface UserTableProps {
  users: User[];
}

type SortField = 'email' | 'balance' | 'tier' | 'loan';
type SortDirection = 'asc' | 'desc';

export function UserTable({ users }: UserTableProps) {
  const [sortField, setSortField] = useState<SortField>('email');
  const [sortDirection, setSortDirection] = useState<SortDirection>('asc');

  const formatBalance = (balance: number): string => {
    if (balance >= 1000) {
      return `$${(balance / 1000).toFixed(3)}`;
    }
    return `$${balance.toFixed(2)}`;
  };

  const formatLoan = (amount: number): string => {
    return `${amount.toFixed(2)} USDT`;
  };

  const handleSort = (field: SortField) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  const sortedUsers = [...users].sort((a, b) => {
    let aValue: any = a[sortField];
    let bValue: any = b[sortField];

    if (sortField === 'tier') {
      aValue = a.tier === UserTier.PRO ? 1 : 0;
      bValue = b.tier === UserTier.PRO ? 1 : 0;
    }

    if (typeof aValue === 'string') {
      aValue = aValue.toLowerCase();
      bValue = bValue.toLowerCase();
    }

    if (sortDirection === 'asc') {
      return aValue > bValue ? 1 : -1;
    } else {
      return aValue < bValue ? 1 : -1;
    }
  });

  const SortButton = ({ field, children }: { field: SortField; children: React.ReactNode }) => (
    <Button
      variant="ghost"
      size="sm"
      onClick={() => handleSort(field)}
      className="h-auto p-0 font-medium text-left justify-start hover:bg-transparent"
    >
      {children}
      <ArrowUpDown size={14} className="ml-2" />
    </Button>
  );

  return (
    <div className="w-full">
      <Table>
        <TableHeader>
          <TableRow className="border-b border-border">
            <TableHead className="w-[300px] text-muted-foreground font-medium">
              <SortButton field="email">Identifier</SortButton>
            </TableHead>
            <TableHead className="text-muted-foreground font-medium">
              <SortButton field="balance">Balance</SortButton>
            </TableHead>
            <TableHead className="text-muted-foreground font-medium">
              <SortButton field="tier">Tier</SortButton>
            </TableHead>
            <TableHead className="text-right text-muted-foreground font-medium">
              <SortButton field="loan">Loan</SortButton>
            </TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {sortedUsers.map((user) => (
            <TableRow key={user.id}>
              <TableCell className="font-medium">{user.email}</TableCell>
              <TableCell>{formatBalance(user.balance)}</TableCell>
              <TableCell>
                <TierBadge tier={user.tier} />
              </TableCell>
              <TableCell className="text-right">{formatLoan(user.loan)}</TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
}