import { useState } from "react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Table, TableBody, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Search, UserRoundPlus } from "lucide-react";
import { Administrator } from "@/types/schema";
import { AdministratorRow } from "./AdministratorRow";

interface AdministratorsTableProps {
  administrators: Administrator[];
}

export function AdministratorsTable({ administrators }: AdministratorsTableProps) {
  const [searchTerm, setSearchTerm] = useState("");

  const filteredAdministrators = administrators.filter(admin =>
    admin.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleAction = (id: string) => {
    // In a real app, this would handle admin actions
    console.log("Action clicked for administrator:", id);
  };

  const handleAddAdministrator = () => {
    // In a real app, this would open a modal or navigate to add admin page
    console.log("Add administrator clicked");
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div className="relative flex-1 max-w-md">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Find by name"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
        <Button
          onClick={handleAddAdministrator}
          className="h-10 w-10 rounded-full bg-lendbloc-blue hover:bg-lendbloc-blue-dark p-0"
        >
          <UserRoundPlus className="h-4 w-4 text-white" />
        </Button>
      </div>

      <div className="rounded-lg border bg-card">
        <Table>
          <TableHeader>
            <TableRow className="hover:bg-transparent">
              <TableHead className="font-semibold">Name</TableHead>
              <TableHead className="font-semibold">Presence</TableHead>
              <TableHead className="font-semibold">Status</TableHead>
              <TableHead className="font-semibold">Action</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredAdministrators.map((administrator) => (
              <AdministratorRow
                key={administrator.id}
                administrator={administrator}
                onAction={handleAction}
              />
            ))}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}