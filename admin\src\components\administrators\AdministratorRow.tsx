import { TableRow, TableCell } from "@/components/ui/table";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar";
import { CircleArrowRight, Star } from "lucide-react";
import { Administrator } from "@/types/schema";
import { formatPresencePercentage } from "@/utils/formatters";
import { StatusBadge } from "./StatusBadge";

interface AdministratorRowProps {
  administrator: Administrator;
  onAction: (id: string) => void;
}

export function AdministratorRow({ administrator, onAction }: AdministratorRowProps) {
  const getPresenceColor = (presence: number) => {
    if (presence >= 95) return "text-lendbloc-blue";
    if (presence >= 85) return "text-lendbloc-blue";
    if (presence >= 70) return "text-yellow-600";
    return "text-orange-500";
  };

  const shouldShowStar = administrator.isCurrentUser || administrator.presence >= 99;

  return (
    <TableRow className="hover:bg-muted/50">
      <TableCell className="font-medium">
        <div className="flex items-center gap-3">
          <Avatar className="h-8 w-8">
            <AvatarImage src={administrator.avatar} alt={administrator.name} />
            <AvatarFallback>{administrator.name.charAt(0)}</AvatarFallback>
          </Avatar>
          <div className="flex items-center gap-2">
            {shouldShowStar && (
              <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
            )}
            <span>{administrator.name}</span>
          </div>
        </div>
      </TableCell>
      <TableCell>
        <span className={getPresenceColor(administrator.presence)}>
          {formatPresencePercentage(administrator.presence)}
        </span>
      </TableCell>
      <TableCell>
        <StatusBadge status={administrator.status} />
      </TableCell>
      <TableCell>
        <Button
          variant="default"
          size="sm"
          className="h-8 w-8 rounded-full bg-lendbloc-blue hover:bg-lendbloc-blue-dark p-0"
          onClick={() => onAction(administrator.id)}
        >
          <CircleArrowRight className="h-4 w-4 text-white" />
        </Button>
      </TableCell>
    </TableRow>
  );
}