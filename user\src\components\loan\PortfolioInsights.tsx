"use client";

import React, { useState } from 'react';
import { Card } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { PortfolioChart } from './PortfolioChart';
import { CryptocurrencyHolding } from './CryptocurrencyHolding';
import { PortfolioInsights as PortfolioInsightsType } from '../../types/schema';
import { TimePeriod } from '../../types/enums';
import { formatPortfolioValue } from '../../utils/formatters';

interface PortfolioInsightsProps {
  portfolioInsights: PortfolioInsightsType;
}

export const PortfolioInsights: React.FC<PortfolioInsightsProps> = ({ portfolioInsights }) => {
  const [selectedPeriod, setSelectedPeriod] = useState<string>(TimePeriod.ALL_TIME);

  return (
    <Card className="h-full p-4">
      <div className="space-y-6">
        <div>
          <h2 className="text-lg font-semibold mb-4">Portfolio insights</h2>
          <PortfolioChart
            data={portfolioInsights.interestEarnedData}
            selectedPeriod={selectedPeriod}
            onPeriodChange={setSelectedPeriod}
          />
        </div>
        
        <Separator />
        
        <div>
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-2xl font-semibold">Portfolio value</h3>
            <div className="text-3xl font-bold">
              {formatPortfolioValue(portfolioInsights.totalValue)}
            </div>
          </div>
          
          <div className="space-y-2">
            {portfolioInsights.holdings.map((holding, index) => (
              <CryptocurrencyHolding key={index} holding={holding} />
            ))}
          </div>
        </div>
      </div>
    </Card>
  );
};