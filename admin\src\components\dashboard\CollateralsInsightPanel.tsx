"use client";

import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { CollateralsData } from "@/types/schema";
import { CryptoHoldingItem } from "./CryptoHoldingItem";
import { formatCurrency } from "@/utils/formatters";
import { BarChart3 } from "lucide-react";

interface CollateralsInsightPanelProps {
  data: CollateralsData;
}

export function CollateralsInsightPanel({ data }: CollateralsInsightPanelProps) {
  return (
    <Card className="p-6 h-full">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-900">Collaterals insight</h3>
        <Button variant="outline" size="sm" className="text-blue-600 border-blue-600 hover:bg-blue-50">
          <BarChart3 size={16} className="mr-2" />
          Show graph
        </Button>
      </div>
      
      <div className="mb-6">
        <div className="text-sm text-gray-500 mb-1">Estimated value</div>
        <div className="text-2xl font-bold text-gray-900">
          {formatCurrency(data.estimatedValue)}
        </div>
      </div>

      <div className="space-y-1">
        {data.holdings.map((holding) => (
          <CryptoHoldingItem key={holding.rank} holding={holding} />
        ))}
      </div>
    </Card>
  );
}