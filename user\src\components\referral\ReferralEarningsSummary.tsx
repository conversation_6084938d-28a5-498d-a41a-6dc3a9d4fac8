"use client";

import { Card } from "@/components/ui/card";
import { ArrowRight } from "lucide-react";

interface ReferralEarningsSummaryProps {
  totalEarnings: number;
}

export function ReferralEarningsSummary({ totalEarnings }: ReferralEarningsSummaryProps) {
  return (
    <Card className="py-4 px-8 shadow-sm rounded-2xl">
      <div className="flex items-center justify-between">
        <h3 className="text-lg text-gray-900">Referral earnings</h3>
        <div className="flex items-center gap-2">
          <div className="text-2xl font-bold text-blue-600">
            ${totalEarnings.toFixed(2)} USDT
          </div>
          <ArrowRight className="w-5 h-5 text-[#466DFF]" />
        </div>
      </div>
    </Card>
  );
}