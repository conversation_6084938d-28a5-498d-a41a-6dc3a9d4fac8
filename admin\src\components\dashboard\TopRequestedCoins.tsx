"use client";

import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { TopCoin } from "@/types/schema";
import { CryptoIcon } from "./CryptoIcon";
import { TrendIndicator } from "./TrendIndicator";
import { Edit3 } from "lucide-react";

interface TopRequestedCoinsProps {
  coins: TopCoin[];
}

export function TopRequestedCoins({ coins }: TopRequestedCoinsProps) {
  return (
    <Card className="p-6">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-900">Top Requested coins</h3>
        <Button variant="outline" size="sm" className="text-blue-600 border-blue-600 hover:bg-blue-50">
          <Edit3 size={16} className="mr-2" />
          Edit list
        </Button>
      </div>
      
      <div className="space-y-3">
        {coins.map((coin, index) => (
          <div key={coin.symbol} className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <CryptoIcon currency={coin.currency} size={16} />
              <div>
                <div className="font-medium text-gray-900">{coin.currency} ({coin.symbol})</div>
              </div>
            </div>
            <TrendIndicator change={coin.change} trend={coin.trend} />
          </div>
        ))}
      </div>
    </Card>
  );
}