"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Download, Search, ChevronDown } from "lucide-react";

interface TransactionHeaderProps {
  selectedDate: string;
  searchQuery: string;
  onDateChange: (date: string) => void;
  onSearchChange: (query: string) => void;
  onExport: () => void;
}

export function TransactionHeader({
  selectedDate,
  searchQuery,
  onDateChange,
  onSearchChange,
  onExport
}: TransactionHeaderProps) {
  return (
    <div className="flex items-center justify-between mb-6">
      <div className="flex items-center gap-4">
        <h1 className="text-2xl font-semibold text-foreground">Transactions center</h1>
        
        <Select value={selectedDate} onValueChange={onDateChange}>
          <SelectTrigger className="w-40">
            <SelectValue placeholder="Select date" />
            <ChevronDown size={16} className="ml-2" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="2025-07-03">July 3, 2025</SelectItem>
            <SelectItem value="2025-07-02">July 2, 2025</SelectItem>
            <SelectItem value="2025-07-01">July 1, 2025</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div className="flex items-center gap-4">
        <div className="relative">
          <Search size={16} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground" />
          <Input
            placeholder="Filter by ID"
            value={searchQuery}
            onChange={(e) => onSearchChange(e.target.value)}
            className="pl-10 w-64"
          />
        </div>
        
        <Button 
          onClick={onExport}
          className="bg-lendbloc-blue hover:bg-lendbloc-blue-dark text-white"
        >
          <Download size={16} className="mr-2" />
          Export
        </Button>
      </div>
    </div>
  );
}