"use client";

// import type { Metadata } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import "./globals.css";
import { AppProvider } from "@/hooks/context/useAppContext";
import dynamic from "next/dynamic";
import { usePathname } from "next/navigation";
import React from "react";
import { Sidebar } from "@/components/general";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

// export const metadata: Metadata = {
//   title: "Admin Panel - LendBloc",
//   description: "Admin dashboard for LendBloc",
// };

const ClientAuthGate = dynamic(() => import("./private/AuthGate").then((m) => m.AuthGate), {
  ssr: false,
});

function Shell({ children }: { children: React.ReactNode }) {
  const pathname = usePathname();

  // Hide sidebars on /auth
  const hideChrome = pathname === "/auth";

  // Desktop-only layout (no mobile/tablet support for admin)
  const layout = (() => {
    if (hideChrome) {
      return {
        containerClass: "min-h-screen",
        sidebarVisible: false as const,
        sidebarMinWidth: "0",
        contentMinWidth: "100vw",
      };
    }

    // Desktop default - admin panel is desktop-only
    return {
      containerClass: "min-h-screen flex",
      sidebarVisible: true as const,
      sidebarMinWidth: "25vw",
      contentMinWidth: "75vw",
    };
  })();

  // Memoize style objects to avoid re-renders
  const sidebarStyle = React.useMemo(
    () => ({ minWidth: layout.sidebarMinWidth }),
    [layout.sidebarMinWidth]
  );
  const contentStyle = React.useMemo(
    () => ({ minWidth: layout.contentMinWidth }),
    [layout.contentMinWidth]
  );

  return (
    <div className={layout.containerClass}>
      {layout.sidebarVisible && (
        <div style={sidebarStyle} aria-hidden={false}>
          <Sidebar />
        </div>
      )}
      <main
        className="min-h-screen"
        style={contentStyle}
        role="main"
      >
        {children}
      </main>
    </div>
  );
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body className={`${geistSans.variable} ${geistMono.variable} antialiased`}>
        <AppProvider>
          <ClientAuthGate>
            <Shell>{children}</Shell>
          </ClientAuthGate>
        </AppProvider>
      </body>
    </html>
  );
}
