"use client";

import React from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ArrowRight } from 'lucide-react';
import { Separator } from '@/components/ui/separator';
import { TopTrendItem as TopTrendItemType } from '../../types/schema';
import TopTrendsTable from '../dashboard/TopTrendsTable';
import { TopTrendAsset } from '@/data/dashboard-data';
import { formatVolume } from '../../utils/formatters';

interface TopTrendsSidebarProps {
  topTrends: TopTrendItemType[];
}

// Convert TopTrendItem to TopTrendAsset format for the table
const convertTopTrendsData = (topTrends: TopTrendItemType[]): TopTrendAsset[] => {
  return topTrends.map((item) => ({
    rank: item.rank,
    asset: item.asset.toString(), // Convert enum to string
    symbol: item.asset.toString(), // Use same value for symbol
    collateralValue: formatVolume(item.collateralValue),
    volume24h: formatVolume(item.volume24h),
    icon: '', // Not used in the table display
    color: '' // Not used in the table display
  }));
};

export const TopTrendsSidebar: React.FC<TopTrendsSidebarProps> = ({ topTrends }) => {
  const convertedTrends = convertTopTrendsData(topTrends);

  return (
    <div className="h-full flex flex-col">
      <Card className="p-6 h-full flex flex-col">
        <div className="overflow-y-auto mb-4">
          <TopTrendsTable trends={convertedTrends} />
        </div>
        <Separator className="my-4" />
        <div className="space-y-3">
          <Button
            variant="outline"
            className="w-full justify-between text-blue-600 border-blue-200 hover:bg-blue-50 h-16 rounded-full flex"
          >
            <div className='w-full flex justify-between text-xl items-center px-5'>
              Demo savings
              <ArrowRight className="w-4 h-4" />
            </div>
          </Button>

          <Button className="w-full bg-blue-600 hover:bg-blue-700 justify-between h-16 rounded-full flex">
            <div className='w-full flex justify-between text-xl items-center px-5'>
              Start real saving
              <ArrowRight className="w-4 h-4" style={{ width: '1.5rem', height: '1.5rem' }} />
            </div>
          </Button>
        </div>
      </Card>
    </div>
  );
};