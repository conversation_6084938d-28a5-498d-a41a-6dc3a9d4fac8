"use client";

import { useState } from "react";
import { Card } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { ChartContainer, ChartTooltip, ChartTooltipContent } from "@/components/ui/chart";
import { useViewportType } from "@/hooks/use-viewport";
import { AreaChart, Area, XAxis, YAxis } from "recharts";
import { Play } from "lucide-react";
import { FaPlay } from "react-icons/fa";

// Import components
import { ReferralPerformanceChart } from "@/components/referral/ReferralPerformanceChart";
import { ReferralEarningsChart } from "@/components/referral/ReferralEarningsChart";
import { PortfolioInsightsChart } from "@/components/referral/PortfolioInsightsChart";
import { ReferralLinkCard } from "@/components/referral/ReferralLinkCard";
import { ReferralEarningsSummary } from "@/components/referral/ReferralEarningsSummary";
import { ProfitCalculatorCard } from "@/components/referral/ProfitCalculatorCard";
import { ReferralInfoPanel } from "@/components/referral/ReferralInfoPanel";

// Import mock data
import {
  mockReferralPerformanceData,
  mockReferralEarningData,
  mockPortfolioData,
  mockRootProps,
  chartConfig,
  formatReferralLink
} from "../../components/referral/referralMockData";

export default function ReferralsPage() {
  const { isMobile, isTablet } = useViewportType();
  const [selectedPeriod, setSelectedPeriod] = useState("All time");
  const [portfolioPeriod, setPortfolioPeriod] = useState("All time");

  const referralLink = formatReferralLink(mockRootProps.referralCode);

  // Mobile and Tablet Layout (< 1200px)
  if (isMobile || isTablet) {
    return (
      <div className="w-full h-screen p-2 sm:p-4 bg-referral-gray-50 overflow-hidden">
        <div className="w-full h-full flex flex-col gap-2 sm:gap-4 overflow-hidden">
          <div className="text-center flex-shrink-0">
            <h1 className="text-xl sm:text-2xl font-bold text-referral-gray-900 mb-2">Earn more with referral</h1>
            <Button variant="ghost" size="sm" className="text-referral-blue hover:bg-blue-50 text-xs sm:text-sm">
              <Play className="w-3 h-3 sm:w-4 sm:h-4 mr-2" />
              How to start earning
            </Button>
          </div>

          <Card className="p-2 sm:p-4 shadow-sm flex-shrink-0">
            <ReferralLinkCard referralLink={referralLink} isMobile />
          </Card>

          <Card className="p-2 sm:p-4 shadow-sm flex-1 min-h-0 max-h-full overflow-hidden">
            <h3 className="font-semibold mb-2 sm:mb-4 text-referral-gray-900 text-sm sm:text-base">Referral earnings</h3>
            <div className="text-xl sm:text-2xl font-bold text-referral-blue mb-2">
              ${mockRootProps.totalEarnings.toFixed(2)} USDT
            </div>
            <div className="flex-1 min-h-0 max-h-full overflow-hidden">
              <ChartContainer config={chartConfig} className="w-full h-full">
                <AreaChart data={mockReferralEarningData}>
                  <XAxis dataKey="month" />
                  <YAxis />
                  <ChartTooltip content={<ChartTooltipContent />} />
                  <Area
                    dataKey="earnings"
                    stroke="#3b82f6"
                    fill="#3b82f6"
                    fillOpacity={0.3}
                  />
                </AreaChart>
              </ChartContainer>
            </div>
          </Card>
        </div>
      </div>
    );
  }

  // Desktop Layout (≥ 1200px)
  return (
    <div className="w-full h-screen p-16 bg-gray-300/50 overflow-hidden">
      <div className="w-full h-full grid grid-cols-1 gap-4 lg:gap-6 xl:grid-cols-[6fr_4fr] overflow-hidden">
        {/* Left Column */}
        <div className="min-h-0 max-h-full flex flex-col gap-4 lg:gap-6 overflow-hidden">
          {/* Header Section */}
          <div className="flex items-center justify-between flex-shrink-0">
            <h1 className="text-2xl md:text-3xl font-bold text-referral-gray-900">Earn more with referral</h1>
            <Button variant="ghost" size="sm" className="text-[#0E4DDB] bg-[#FFFFFF] hover:bg-blue-50 border-2 border-[#0E4DDB] rounded-xl py-5 px-3 md:px-4 text-sm md:text-base">
              <FaPlay className="w-4 h-4 mr-2" />
              How to start earning
            </Button>
          </div>

          {/* Referral Performance Metrics */}
          <div className="flex-1 min-h-0 max-h-[40%] overflow-hidden">
            <ReferralPerformanceChart
              data={mockReferralPerformanceData}
              selectedPeriod={selectedPeriod}
              onPeriodChange={setSelectedPeriod}
              chartConfig={chartConfig}
            />
          </div>

          {/* Referral Earning Metrics */}
          <div className="flex-shrink-0 h-[50%] space-y-4">
            <ReferralEarningsChart
              data={mockReferralEarningData}
              chartConfig={chartConfig}
            />
            {/* Referral Earnings Summary */}
            <div className="flex-shrink-0">
              <ReferralEarningsSummary totalEarnings={mockRootProps.totalEarnings} />
            </div>

            {/* Calculate Your Profit */}
            <div className="flex-shrink-0">
              <ProfitCalculatorCard />
            </div>
          </div>

        </div>

        {/* Right Column */}
        <div className="min-h-0 max-h-full overflow-hidden ">
          <Card className="h-full p-8 space-y-4 shadow-sm bg-white border border-gray-200 overflow-hidden">
            {/* Referral Info */}
            <div className="flex-shrink-0">
              <ReferralInfoPanel />
            </div>

            {/* Referral Link */}
            <div className="flex-shrink-0">
              <ReferralLinkCard referralLink={referralLink} />
            </div>

            {/* Portfolio Insights */}
            <div className="flex-1 min-h-0 max-h-full overflow-hidden">
              <PortfolioInsightsChart
                data={mockPortfolioData}
                selectedPeriod={portfolioPeriod}
                onPeriodChange={setPortfolioPeriod}
                chartConfig={chartConfig}
              />
            </div>
          </Card>
        </div>
      </div>
    </div>
  );
}