"use client";

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { SavingsAction } from "@/types/enums";
import { formatCryptoAmount } from "@/utils/formatters";

interface ReferralsTransaction {
  id: string;
  time: string;
  user: string;
  referral: SavingsAction;
  amount: number;
  currency: string;
}

interface ReferralsTableProps {
  transactions: ReferralsTransaction[];
}

export function ReferralsTable({ transactions }: ReferralsTableProps) {
  const getReferralColor = (referral: SavingsAction) => {
    switch (referral) {
      case SavingsAction.SAVE:
        return 'text-blue-600';
      case SavingsAction.UNSAVE:
        return 'text-red-600';
      default:
        return 'text-foreground';
    }
  };

  const getAmountColor = (currency: string) => {
    switch (currency) {
      case 'BTC':
        return 'text-orange-600';
      case 'USDT':
        return 'text-orange-600';
      default:
        return 'text-green-600';
    }
  };

  return (
    <div className="w-full">
      <Table>
        <TableHeader>
          <TableRow className="border-b border-border">
            <TableHead className="text-muted-foreground font-medium">Time</TableHead>
            <TableHead className="text-muted-foreground font-medium">ID</TableHead>
            <TableHead className="text-muted-foreground font-medium">User</TableHead>
            <TableHead className="text-muted-foreground font-medium">Referral</TableHead>
            <TableHead className="text-muted-foreground font-medium">Amount</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {transactions.map((transaction, index) => (
            <TableRow key={`${transaction.id}-${index}`}>
              <TableCell className="font-medium">{transaction.time}</TableCell>
              <TableCell>{transaction.id}</TableCell>
              <TableCell>{transaction.user}</TableCell>
              <TableCell className={getReferralColor(transaction.referral)}>
                {transaction.referral}
              </TableCell>
              <TableCell className={getAmountColor(transaction.currency)}>
                {formatCryptoAmount(transaction.amount, transaction.currency)}
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
}