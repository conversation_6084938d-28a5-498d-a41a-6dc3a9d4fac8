"use client";

import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ResponsiveContainer } from "recharts";
import { ChartContainer, ChartTooltip, ChartTooltipContent } from "@/components/ui/chart";
import { ChartDataPoint } from "@/types/schema";

interface EstimatedProfitsChartProps {
  data: ChartDataPoint[];
}

const chartConfig = {
  value: {
    label: "Profits",
    color: "#3B82F6",
  },
};

export function EstimatedProfitsChart({ data }: EstimatedProfitsChartProps) {
  return (
    <ChartContainer config={chartConfig} className="w-full h-full">
      <AreaChart data={data} margin={{ top: 20, right: 30, left: 20, bottom: 20 }}>
        <defs>
          <linearGradient id="profitsGradient" x1="0" y1="0" x2="0" y2="1">
            <stop offset="0%" stopColor="#3B82F6" stopOpacity={0.3} />
            <stop offset="100%" stopColor="#3B82F6" stopOpacity={0.05} />
          </linearGradient>
        </defs>
        <XAxis 
          dataKey="time" 
          axisLine={false}
          tickLine={false}
          tick={{ fontSize: 12, fill: '#6B7280' }}
        />
        <YAxis hide />
        <ChartTooltip content={<ChartTooltipContent />} />
        <Area
          type="monotone"
          dataKey="value"
          stroke="#3B82F6"
          strokeWidth={2}
          fill="url(#profitsGradient)"
          dot={false}
        />
      </AreaChart>
    </ChartContainer>
  );
}