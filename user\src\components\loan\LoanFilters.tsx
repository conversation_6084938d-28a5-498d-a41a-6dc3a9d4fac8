"use client";

import React from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Plus, Download, Search } from 'lucide-react';
import { LoanHealth } from '../../types/enums';
import Image from 'next/image';

interface LoanFiltersProps {
  searchTerm: string;
  onSearchChange: (value: string) => void;
  healthFilter: string;
  onHealthFilterChange: (value: string) => void;
  onGetLoan: () => void;
  onDownloadCSV: () => void;
}

export const LoanFilters: React.FC<LoanFiltersProps> = ({
  searchTerm,
  onSearchChange,
  healthFilter,
  onHealthFilterChange,
  onGetLoan,
  onDownloadCSV,
}) => {
  const healthColorClasses: { [key: string]: string } = {
    [LoanHealth.GREEN]: 'text-green-600',
    [LoanHealth.YELLOW]: 'text-yellow-600',
    [LoanHealth.RED]: 'text-red-600',
    all: 'text-black',
  };

  const selectedColorClass = healthColorClasses[healthFilter] || 'text-black';

  return (
    <div className="flex flex-col gap-3 mb-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-semibold text-blue-600">My loans</h1>
        <Button
          onClick={onDownloadCSV}
          variant="outline"
          size="sm"
          className="gap-2 bg-blue-600 text-white py-5 rounded-2xl "
        >
          <Image src={"/icons/general/csvD.svg"} alt="Download CSV" width={16} height={16} />
          Download CSV
        </Button>
      </div>

      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center mt-8">
        <div className='w-[35%] pr-12'>
          <Button
            onClick={onGetLoan}
            className="bg-blue-600 hover:bg-blue-700 gap-2 px-6 w-full h-[4rem] rounded-3xl text-lg"
          >
            <Plus className="w-18 h-18" style={{ width: '2rem', height: '2rem' }} />
            Get loan
          </Button>
        </div>

        <div className="relative w-full sm:w-[35%]">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" style={{ width: '1.5rem', height: '1.5rem' }}/>
          <Input
            placeholder="Find loan"
            value={searchTerm}
            onChange={(e) => onSearchChange(e.target.value)}
            className="pl-10 bg-gray-300 border-gray-200 w-full h-[4rem] rounded-3xl text-3xl"
          />
        </div>

        <div className="flex items-center gap-2 w-full sm:w-[30%] bg-gray-100 p-2 rounded-3xl">
          <span className="text-lg font-medium">Health:</span>
          <Select value={healthFilter} onValueChange={onHealthFilterChange}>
            <SelectTrigger className={`w-full text-lg font-semibold bg-gray-100 border-0 shadow-none capitalize ${selectedColorClass}`} style={{ height: '3rem' }}>
              <SelectValue placeholder="Select health" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All</SelectItem>
              <SelectItem value={LoanHealth.GREEN} className='text-green-600'>Green</SelectItem>
              <SelectItem value={LoanHealth.YELLOW} className='text-yellow-600'>Yellow</SelectItem>
              <SelectItem value={LoanHealth.RED} className='text-red-600'>Red</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>
    </div>
  );
};