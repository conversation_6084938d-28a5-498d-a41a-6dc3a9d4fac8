"use client";

import { 
  Bitcoin, 
  Diamond, 
  AlignStartHorizontal, 
  BadgeEuro, 
  BadgeIndianRupee,
  Mountain,
  BadgeDollarSign,
  BadgeSwissFranc,
  MessageSquareX
} from "lucide-react";
import { CryptoCurrency } from "@/types/enums";

interface CryptoIconProps {
  currency: CryptoCurrency;
  size?: number;
  className?: string;
}

const iconMap = {
  [CryptoCurrency.BITCOIN]: Bitcoin,
  [CryptoCurrency.ETHEREUM]: Diamond,
  [CryptoCurrency.SOLANA]: AlignStartHorizontal,
  [CryptoCurrency.BNB]: BadgeEuro,
  [CryptoCurrency.DASH]: BadgeIndianRupee,
  [CryptoCurrency.AVALANCHE]: Mountain,
  [CryptoCurrency.SHIBA_INU]: BadgeDollarSign,
  [CryptoCurrency.PEPE]: BadgeSwissFranc,
  [CryptoCurrency.XRP]: MessageSquareX,
};

const colorMap = {
  [CryptoCurrency.BITCOIN]: "text-orange-500",
  [CryptoCurrency.ETHEREUM]: "text-purple-500",
  [CryptoCurrency.SOLANA]: "text-green-500",
  [CryptoCurrency.BNB]: "text-yellow-500",
  [CryptoCurrency.DASH]: "text-blue-500",
  [CryptoCurrency.AVALANCHE]: "text-red-500",
  [CryptoCurrency.SHIBA_INU]: "text-orange-400",
  [CryptoCurrency.PEPE]: "text-green-400",
  [CryptoCurrency.XRP]: "text-gray-800",
};

export function CryptoIcon({ currency, size = 20, className = "" }: CryptoIconProps) {
  const IconComponent = iconMap[currency];
  const colorClass = colorMap[currency];
  
  return (
    <div className={`rounded-full p-2 bg-gray-100 ${className}`}>
      <IconComponent size={size} className={colorClass} />
    </div>
  );
}