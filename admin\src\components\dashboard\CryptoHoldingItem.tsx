"use client";

import { CryptoHolding } from "@/types/schema";
import { CryptoIcon } from "./CryptoIcon";
import { formatCryptoAmount } from "@/utils/formatters";

interface CryptoHoldingItemProps {
  holding: CryptoHolding;
}

export function CryptoHoldingItem({ holding }: CryptoHoldingItemProps) {
  return (
    <div className="flex items-center justify-between py-2">
      <div className="flex items-center gap-3">
        <span className="text-sm text-gray-500 w-6">#{holding.rank}</span>
        <CryptoIcon currency={holding.currency} size={16} />
        <span className="font-medium text-gray-900">{holding.currency}</span>
      </div>
      <div className="text-right">
        <div className="text-sm font-medium text-gray-900">
          {formatCryptoAmount(holding.amount, holding.symbol)}
        </div>
      </div>
    </div>
  );
}