"use client";

import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid } from "recharts";
import { ChartContainer, ChartTooltip, ChartTooltipContent } from "@/components/ui/chart";
import { Button } from "@/components/ui/button";
import { ChartPeriod } from "@/app/userManagementMockData";

interface ChartData {
  day: string;
  value: number;
}

interface UserAnalyticsChartProps {
  data: ChartData[];
  selectedPeriod: ChartPeriod;
  onPeriodChange: (period: ChartPeriod) => void;
}

const chartConfig = {
  value: {
    label: "New Users",
    color: "var(--color-chart-line)",
  },
};

export function UserAnalyticsChart({ data, selectedPeriod, onPeriodChange }: UserAnalyticsChartProps) {
  const periods = [
    { key: ChartPeriod.WEEK, label: "W" },
    { key: ChartPeriod.MONTH, label: "M" },
    { key: ChartPeriod.YEAR, label: "Y" },
    { key: ChartPeriod.YEAR_TO_DATE, label: "YTD" },
  ];

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold">New users insight</h3>
        <div className="flex gap-1">
          {periods.map((period) => (
            <Button
              key={period.key}
              variant={selectedPeriod === period.key ? "default" : "ghost"}
              size="sm"
              onClick={() => onPeriodChange(period.key)}
              className={`px-3 py-1 text-xs h-7 ${
                selectedPeriod === period.key 
                  ? "bg-lendbloc-blue text-white hover:bg-lendbloc-blue-dark" 
                  : "text-muted-foreground hover:text-foreground hover:bg-muted"
              }`}
            >
              {period.label}
            </Button>
          ))}
        </div>
      </div>
      
      <div className="h-[200px]">
        <ChartContainer config={chartConfig} className="w-full h-full">
          <LineChart data={data} margin={{ top: 5, right: 5, left: 5, bottom: 5 }}>
            <CartesianGrid strokeDasharray="3 3" stroke="var(--color-border)" />
            <XAxis 
              dataKey="day" 
              axisLine={false}
              tickLine={false}
              tick={{ fontSize: 12, fill: "var(--color-muted-foreground)" }}
            />
            <YAxis 
              axisLine={false}
              tickLine={false}
              tick={{ fontSize: 12, fill: "var(--color-muted-foreground)" }}
            />
            <ChartTooltip content={<ChartTooltipContent />} />
            <Line
              type="monotone"
              dataKey="value"
              stroke="var(--color-chart-line)"
              strokeWidth={3}
              dot={{ fill: "var(--color-chart-line)", strokeWidth: 2, r: 4 }}
              activeDot={{ r: 6, stroke: "var(--color-chart-line)", strokeWidth: 2 }}
            />
          </LineChart>
        </ChartContainer>
      </div>
    </div>
  );
}