// Import existing formatters
import { formatPresencePercentage, formatChatTime } from '@/utils/formatters';

export const formatPresenceStatus = (percentage: number | null): string => {
  if (percentage === null) return 'Absent';
  return `${percentage}%`;
};

export const formatTimeLeft = (minutes: number): string => {
  const hours = Math.floor(minutes / 60);
  const mins = minutes % 60;
  
  if (hours > 0) {
    return `${String(hours).padStart(2, '0')}:${String(mins).padStart(2, '0')} hours left`;
  }
  return `${String(mins).padStart(2, '0')}:${String(mins).padStart(2, '0')} mins left`;
};

export const formatPhoneNumber = (countryCode: string, number: string): string => {
  return `${countryCode} ${number}`;
};

// Re-export existing formatters for convenience
export { formatPresencePercentage, formatChatTime };