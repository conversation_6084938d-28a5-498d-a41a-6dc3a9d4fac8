"use client";

import { Card } from "@/components/ui/card";
import { Calculator, ArrowRight } from "lucide-react";
import Image from "next/image";

export function ProfitCalculatorCard() {
  return (
    <Card className="py-4 px-8 shadow-sm rounded-2xl">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div className="w-8 h-8 bg-gray-100 rounded-lg flex items-center justify-center">
            <Image 
              src="/images/calc.svg"
              alt="Calculator Icon"
              width={20}
              height={20}
              className="w-7 h-7"
            />
          </div>
          <span className="font-medium text-gray-900">Calculate Your Profit</span>
        </div>
        <ArrowRight className="w-5 h-5 text-gray-400" />
      </div>
    </Card>
  );
}