"use client";

import { <PERSON>Check } from "lucide-react";
import { Badge } from "@/components/ui/badge";

interface TwoFactorStatusProps {
  isEnabled: boolean;
  onToggle: () => void;
}

export function TwoFactorStatus({ isEnabled }: TwoFactorStatusProps) {
  return (
    <div className="flex items-center gap-3 py-3">
      <div className="flex items-center gap-2">
        <ShieldCheck className="h-5 w-5 text-lendbloc-blue" />
        <span className="text-sm font-medium text-foreground">
          2FA is {isEnabled ? 'activated' : 'deactivated'}
        </span>
      </div>
      {isEnabled && (
        <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
          ✓ Active
        </Badge>
      )}
    </div>
  );
}