"use client";

import { useState, useMemo } from "react";
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { UserSearch } from "@/components/user-management/UserSearch";
import { UserTable } from "@/components/user-management/UserTable";
import { UserAnalyticsChart } from "@/components/user-management/UserAnalyticsChart";
import { UserStatsCard } from "@/components/user-management/UserStatsCard";
import { mockQuery, ChartPeriod } from "@/app/userManagementMockData";

export default function UsersPage() {
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedPeriod, setSelectedPeriod] = useState<ChartPeriod>(ChartPeriod.WEEK);

  const filteredUsers = useMemo(() => {
    if (!searchQuery) return mockQuery.users;
    return mockQuery.users.filter(user =>
      user.email.toLowerCase().includes(searchQuery.toLowerCase())
    );
  }, [searchQuery]);

  return (
    <div className="w-full min-h-full p-6 bg-background">
      <div className="h-full grid grid-cols-1 gap-6 xl:grid-cols-[6fr_4fr]">
        <div className="w-full min-h-full flex flex-col gap-6">
          <div className="bg-white rounded-lg p-6 border">
            <div className="flex items-center justify-between mb-6">
              <div>
                <h2 className="text-xl font-semibold text-foreground">Users management</h2>
              </div>
              <UserSearch onSearch={setSearchQuery} />
            </div>
            <UserTable users={filteredUsers} />
          </div>
        </div>

        <div className="w-full min-h-full flex flex-col gap-6">
          <Card className="p-6 h-1/2">
            <UserAnalyticsChart
              data={mockQuery.chartData}
              selectedPeriod={selectedPeriod}
              onPeriodChange={setSelectedPeriod}
            />
          </Card>

          <UserStatsCard stats={mockQuery.userStats} />
        </div>
      </div>
    </div>
  );
}