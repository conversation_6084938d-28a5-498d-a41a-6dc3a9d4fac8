import { TimePeriod, CryptoCurrency } from '../types/enums';

export const mockRootProps = {
  estimatedProfits: 4568.05,
  selectedTimePeriod: TimePeriod.DAY,
  user: {
    name: '<PERSON>',
    role: 'Solar Administrator',
    avatar: 'https://i.pravatar.cc/40?img=1',
    id: '004857'
  },
  profitsChartData: [
    { time: '00:00', value: 3200 },
    { time: '04:00', value: 3800 },
    { time: '08:00', value: 2900 },
    { time: '12:00', value: 4200 },
    { time: '16:00', value: 4568 },
    { time: '20:00', value: 4800 }
  ],
  transactionsData: [
    { time: '00:00', Loans: 1200, Sending: 741, Buying: 374, Savings: 987, Receives: 729, Sellings: 278 },
    { time: '04:00', Loans: 1100, Sending: 650, Buying: 420, Savings: 890, Receives: 680, Sellings: 320 },
    { time: '08:00', Loans: 1350, Sending: 780, Buying: 350, Savings: 1050, Receives: 750, Sellings: 250 },
    { time: '12:00', Loans: 1400, Sending: 820, Buying: 400, Savings: 1100, Receives: 800, Sellings: 300 },
    { time: '16:00', Loans: 1250, Sending: 700, Buying: 380, Savings: 950, Receives: 720, Sellings: 280 },
    { time: '20:00', Loans: 1300, Sending: 750, Buying: 360, Savings: 1000, Receives: 770, Sellings: 290 }
  ],
  collateralsData: {
    estimatedValue: 56320.14,
    holdings: [
      { rank: 1, currency: CryptoCurrency.BITCOIN, symbol: 'BTC', amount: 0.12340000, icon: 'bitcoin' },
      { rank: 2, currency: CryptoCurrency.ETHEREUM, symbol: 'ETH', amount: 2.3567890, icon: 'ethereum' },
      { rank: 3, currency: CryptoCurrency.SOLANA, symbol: 'SOL', amount: 45.23561234, icon: 'solana' },
      { rank: 4, currency: CryptoCurrency.BNB, symbol: 'BNB', amount: 788.32334477, icon: 'bnb' },
      { rank: 5, currency: CryptoCurrency.DASH, symbol: 'DASH', amount: 12.26890000, icon: 'dash' }
    ]
  },
  topRequestedCoins: [
    { currency: CryptoCurrency.AVALANCHE, symbol: 'AVAX', change: 41.98, trend: 'up' as const },
    { currency: CryptoCurrency.SHIBA_INU, symbol: 'SHIB', change: 20.78, trend: 'up' as const },
    { currency: CryptoCurrency.PEPE, symbol: 'PEPE', change: 22.46, trend: 'up' as const },
    { currency: CryptoCurrency.XRP, symbol: 'XRP', change: 14.76, trend: 'up' as const }
  ]
};