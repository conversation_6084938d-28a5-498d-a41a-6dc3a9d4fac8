"use client";

import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { UserAccountData } from "@/types/myAccountSchema";

interface UserProfileHeaderProps {
  user: UserAccountData;
}

export function UserProfileHeader({ user }: UserProfileHeaderProps) {
  return (
    <div className="flex items-center gap-4 mb-6">
      <div className="relative">
        <Avatar className="h-16 w-16">
          <AvatarImage src={user.avatar} alt={user.name} />
          <AvatarFallback className="text-lg font-semibold">
            {user.name.split(' ').map(n => n[0]).join('')}
          </AvatarFallback>
        </Avatar>
      </div>
      
      <div className="flex-1">
        <div className="flex items-center gap-3 mb-1">
          <h1 className="text-2xl font-bold text-foreground">{user.name}</h1>
          <Badge variant="secondary" className="bg-amber-100 text-amber-800 border-amber-200">
            ⭐ {user.role}
          </Badge>
        </div>
        
        <div className="flex items-center gap-2">
          <span className="text-sm text-muted-foreground">Presence score</span>
          <Badge 
            variant="outline" 
            className="bg-green-50 text-green-700 border-green-200 font-semibold"
          >
            {user.presenceScore}%
          </Badge>
        </div>
      </div>
    </div>
  );
}