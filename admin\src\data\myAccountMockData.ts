// Import existing types and enums
import { ChatMessage } from '@/types/schema';

// Mock data for My Account page
export const mockAccountData = {
  user: {
    id: '1',
    name: '<PERSON>',
    role: 'Super Administrator',
    avatar: 'https://i.pravatar.cc/80?img=1',
    email: '<PERSON><PERSON>@gmail.com',
    phone: {
      countryCode: '+1',
      number: '12345 678'
    },
    presenceScore: 99,
    twoFactorEnabled: true
  },
  presenceLogs: [
    {
      id: '1',
      date: new Date(),
      label: 'Today' as const,
      percentage: null,
      timeLeft: 27,
      status: 'active' as const
    },
    {
      id: '2',
      date: new Date('2025-07-02'),
      label: 'July 2, 2025' as const,
      percentage: 97,
      timeLeft: null,
      status: 'completed' as const
    },
    {
      id: '3',
      date: new Date('2025-07-01'),
      label: 'July 1, 2025' as const,
      percentage: 97,
      timeLeft: null,
      status: 'completed' as const
    },
    {
      id: '4',
      date: new Date('2025-06-31'),
      label: 'June 31, 2025' as const,
      percentage: null,
      timeLeft: null,
      status: 'absent' as const
    },
    {
      id: '5',
      date: new Date('2025-06-30'),
      label: 'June 30, 2025' as const,
      percentage: 80,
      timeLeft: null,
      status: 'completed' as const
    }
  ]
};

// Reuse existing chat messages from administrators page
export const mockChatMessages: ChatMessage[] = [
  {
    id: '1',
    sender: 'George Pearson',
    message: 'What are you guys doing?',
    timestamp: new Date('2025-05-29T12:06:00'),
    avatar: 'https://i.pravatar.cc/40?img=11'
  },
  {
    id: '2',
    sender: 'Kathy Brewer',
    message: 'What are you guys doing?',
    timestamp: new Date('2025-05-29T12:40:00'),
    avatar: 'https://i.pravatar.cc/40?img=4'
  },
  {
    id: '3',
    sender: 'Timothy Lucas',
    message: 'What are you guys doing?',
    timestamp: new Date('2025-01-15T14:06:00'),
    avatar: 'https://i.pravatar.cc/40?img=12'
  },
  {
    id: '4',
    sender: 'Timothy Lucas',
    message: 'Are you sure?',
    timestamp: new Date('2025-01-15T14:06:00'),
    avatar: 'https://i.pravatar.cc/40?img=12'
  },
  {
    id: '5',
    sender: 'Timothy Lucas',
    message: 'This is absurd!',
    timestamp: new Date('2025-01-15T14:06:00'),
    avatar: 'https://i.pravatar.cc/40?img=12'
  },
  {
    id: '6',
    sender: 'Jeremy Powell',
    message: 'Amazing',
    timestamp: new Date('2025-01-15T13:06:00'),
    avatar: 'https://i.pravatar.cc/40?img=13'
  },
  {
    id: '7',
    sender: 'Patricia Ford',
    message: 'Hello!',
    timestamp: new Date('2025-01-15T13:06:00'),
    avatar: 'https://i.pravatar.cc/40?img=14'
  },
  {
    id: '8',
    sender: 'Elisa Gray',
    message: 'What are you guys doing?',
    timestamp: new Date('2025-01-15T12:06:00'),
    avatar: 'https://i.pravatar.cc/40?img=15'
  }
];