"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";
import React from "react";
import { SidebarItem } from "@/components/general";
import { useAppContext } from "@/hooks/context/useAppContext";

type Item = {
  href: string;
  label: string;
  icon: React.ReactNode;
  trailing?: React.ReactNode;
  onClick?: () => void;
};

const Logo = () => {
  return (
    <div className="flex flex-col items-center justify-center px-6 py-6 md:py-7 lg:py-8">
      <img 
        src="/images/logoLB.svg" 
        alt="LENDBLOC Logo" 
        className="h-6 w-auto md:h-5 lg:h-16"
      />
      <div className="text-sm text-gray-600 font-medium">
        ADMIN PANEL
      </div>
    </div>
  );
};

const Sidebar: React.FC = () => {
  const pathname = usePathname();
  const app = useAppContext();

  const handleLogout = () => {
    app.devQuickLogout();
  };

  const items: Item[] = [
    {
      href: "/",
      label: "Dashboard",
      icon: (
        <svg width="22" height="22" viewBox="0 0 24 24" fill="none">
          <rect x="3" y="3" width="8" height="8" rx="2" stroke="white" strokeWidth="1.8"/>
          <rect x="13" y="3" width="8" height="8" rx="2" stroke="white" strokeWidth="1.8"/>
          <rect x="3" y="13" width="8" height="8" rx="2" stroke="white" strokeWidth="1.8"/>
          <rect x="13" y="13" width="8" height="8" rx="2" stroke="white" strokeWidth="1.8"/>
        </svg>
      )
    },
    {
      href: "/user-management",
      label: "User Management",
      icon: (
        <svg width="22" height="22" viewBox="0 0 24 24" fill="none">
          <circle cx="12" cy="8" r="3.2" stroke="white" strokeWidth="1.8"/>
          <path d="M5 19c0-3.3 3.1-6 7-6s7 2.7 7 6" stroke="white" strokeWidth="1.8" strokeLinecap="round"/>
        </svg>
      ),
    },
    {
      href: "/transactions-center",
      label: "Transactions Center",
      icon: (
        <svg width="22" height="22" viewBox="0 0 24 24" fill="none">
          <path d="M17 9l-5 5-5-5" stroke="white" strokeWidth="1.8" strokeLinecap="round" strokeLinejoin="round"/>
          <path d="M21 12c0 4.97-4.03 9-9 9s-9-4.03-9-9 4.03-9 9-9 9 4.03 9 9z" stroke="white" strokeWidth="1.8"/>
        </svg>
      ),
    },
    {
      href: "/administrators",
      label: "Administrators",
      icon: (
          <svg width="22" height="22" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M12 11C14.2091 11 16 9.20914 16 7C16 4.79086 14.2091 3 12 3C9.79086 3 8 4.79086 8 7C8 9.20914 9.79086 11 12 11Z" stroke="white" strokeWidth="1.8" strokeLinecap="round" strokeLinejoin="round"/>
              <path d="M18 21C18 18.2386 15.3137 16 12 16C8.68629 16 6 18.2386 6 21" stroke="white" strokeWidth="1.8" strokeLinecap="round" strokeLinejoin="round"/>
          </svg>
      ),
    },
    {
      href: "/my-account",
      label: "My Account",
      icon: (
        <svg width="22" height="22" viewBox="0 0 24 24" fill="none">
          <circle cx="12" cy="12" r="3" stroke="white" strokeWidth="1.8"/>
          <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z" stroke="white" strokeWidth="1.8"/>
        </svg>
      ),
    },
    {
      href: "#",
      label: "LogOut",
      icon: (
          <svg width="22" height="22" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M15 3H19C19.5304 3 20.0391 3.21071 20.4142 3.58579C20.7893 3.96086 21 4.46957 21 5V19C21 19.5304 20.7893 20.0391 20.4142 20.4142C20.0391 20.7893 19.5304 21 19 21H15" stroke="white" strokeWidth="1.8" strokeLinecap="round" strokeLinejoin="round"/>
              <path d="M10 17L15 12L10 7" stroke="white" strokeWidth="1.8" strokeLinecap="round" strokeLinejoin="round"/>
              <path d="M15 12H3" stroke="white" strokeWidth="1.8" strokeLinecap="round" strokeLinejoin="round"/>
          </svg>
      ),
      onClick: handleLogout,
    },
  ];

  return (
    <aside
      className="
        hidden md:flex md:flex-col
        w-full shrink-0 border-r border-slate-200
        bg-white text-slate-700
        h-screen sticky top-0
      "
    >
      <Link href="/" aria-label="Admin Panel" className="select-none">
        <Logo />
      </Link>

      <nav className="mt-6 xl:mt-12 flex-1 xl:pl-6 pl-2">
        <ul className="flex flex-col gap-18">
          {items.map((item) => {
            const active =
              item.href === "/"
                ? pathname === "/"
                : pathname?.startsWith(item.href);
            return (
              <li key={item.href} className="pl-6 font-semibold">
                <SidebarItem
                  href={item.href}
                  label={item.label}
                  active={!!active}
                  icon={item.icon}
                  trailing={item.trailing}
                  onClick={item.onClick}
                />
              </li>
            );
          })}
        </ul>
      </nav>
    </aside>
  );
};

export default Sidebar;