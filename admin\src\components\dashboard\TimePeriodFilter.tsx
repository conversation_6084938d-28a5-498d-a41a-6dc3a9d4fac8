"use client";

import { But<PERSON> } from "@/components/ui/button";
import { TimePeriod } from "@/types/enums";

interface TimePeriodFilterProps {
  selectedPeriod: TimePeriod;
  onPeriodChange: (period: TimePeriod) => void;
}

const periods = [
  TimePeriod.DAY,
  TimePeriod.WEEK,
  TimePeriod.MONTH,
  TimePeriod.SIX_MONTHS,
  TimePeriod.YEAR_TO_DATE,
  TimePeriod.ALL
];

export function TimePeriodFilter({ selectedPeriod, onPeriodChange }: TimePeriodFilterProps) {
  return (
    <div className="flex gap-1">
      {periods.map((period) => (
        <Button
          key={period}
          variant={selectedPeriod === period ? "default" : "ghost"}
          size="sm"
          onClick={() => onPeriodChange(period)}
          className={`px-3 py-1 text-sm ${
            selectedPeriod === period 
              ? "bg-blue-600 text-white hover:bg-blue-700" 
              : "text-gray-600 hover:text-gray-900 hover:bg-gray-100"
          }`}
        >
          {period}
        </Button>
      ))}
    </div>
  );
}