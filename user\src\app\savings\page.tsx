"use client";

import React, { useState } from 'react';
import { useViewportType } from "@/hooks/use-viewport";
import { SavingsOnboarding } from '@/components/savings/SavingsOnboarding';
import { SavingsDashboard } from '@/components/savings/SavingsDashboard';
import { mockRootProps } from '@/data/savingsDashboardMockData';
import { TopTrendsSidebar } from '@/components/savings/TopTrendsSidebar';

export default function SavingsPage() {
  const { isMobile, isTablet } = useViewportType();
  const [showOnboarding, setShowOnboarding] = useState(true);

  const handleOnboardingComplete = () => {
    setShowOnboarding(false);
  };

  // Mobile and Tablet Layout (< 1200px)
  if (isMobile || isTablet) {
    return (
      <div className="w-full h-screen p-2 sm:p-4 bg-red-300/50 overflow-hidden">
        <div className="w-full h-full flex flex-col gap-2 sm:gap-4 overflow-hidden">
          {showOnboarding ? (
            <>
              <div className="flex-1 min-h-0 max-h-full overflow-hidden">
                <SavingsOnboarding
                  slides={mockRootProps.onboardingSlides}
                  onComplete={handleOnboardingComplete}
                />
              </div>
              <div className="flex-shrink-0">
                <TopTrendsSidebar topTrends={mockRootProps.topTrends} />
              </div>
            </>
          ) : (
            <>
              <div className="flex-1 min-h-0 max-h-full overflow-hidden">
                <SavingsDashboard
                  savingsData={mockRootProps.savingsData}
                  topTrends={mockRootProps.topTrends}
                />
              </div>
              <div className="flex-shrink-0">
                <TopTrendsSidebar topTrends={mockRootProps.topTrends} />
              </div>
            </>
          )}
        </div>
      </div>
    );
  }

  // Desktop Layout (≥ 1200px)
  return (
    <div className="w-full h-screen p-16 bg-gray-300/50 overflow-hidden">
      <div className="w-full h-full grid grid-cols-1 gap-4 lg:gap-6 xl:grid-cols-[6fr_4fr] overflow-hidden">
        {showOnboarding ? (
          <>
            <div className="min-h-0 max-h-full overflow-hidden">
              <SavingsOnboarding
                slides={mockRootProps.onboardingSlides}
                onComplete={handleOnboardingComplete}
              />
            </div>
          </>
        ) : (
          <>
            <div className="min-h-0 max-h-full overflow-hidden">
              <SavingsDashboard
                savingsData={mockRootProps.savingsData}
                topTrends={mockRootProps.topTrends}
              />
            </div>
          </>
        )}
        <div className="min-h-0 max-h-full overflow-hidden">
          <TopTrendsSidebar topTrends={mockRootProps.topTrends} />
        </div>
      </div>
    </div>
  );
}