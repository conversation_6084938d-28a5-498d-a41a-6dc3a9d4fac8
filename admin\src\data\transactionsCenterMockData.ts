import { TransactionStatus, TransactionCategory, SavingsAction, InsightType } from '../types/enums';

// Mock data for transactions center
export const mockRootProps = {
  selectedDate: '2025-07-03',
  selectedTab: TransactionCategory.LOANS,
  searchQuery: ''
};

export const mockQuery = {
  loansTransactions: [
    {
      id: 'JD82XM888329',
      time: '00:12',
      collateral: 0.0025,
      collateralCurrency: 'BTC',
      loan: 277.00,
      loanCurrency: 'USDT',
      status: TransactionStatus.PROCESSING,
      isHeld: true
    },
    {
      id: 'JD82XM888329', 
      time: '07:35',
      collateral: 0.0025,
      collateralCurrency: 'BTC',
      loan: 277.00,
      loanCurrency: 'USDT',
      status: TransactionStatus.PROCESSING,
      isHeld: false
    },
    {
      id: 'JD82XM888329',
      time: '22:10', 
      collateral: 0.0025,
      collateralCurrency: 'BTC',
      loan: 277.00,
      loanCurrency: 'USDT',
      status: TransactionStatus.PROCESSING,
      isHeld: false
    },
    {
      id: 'JD82XM888329',
      time: '23:14',
      collateral: 0.0025,
      collateralCurrency: 'BTC',
      loan: 277.00,
      loanCurrency: 'USDT',
      status: TransactionStatus.COMPLETED,
      isHeld: false
    },
    {
      id: 'JD82XM888329',
      time: '23:14',
      collateral: 0.0025,
      collateralCurrency: 'BTC', 
      loan: 277.00,
      loanCurrency: 'USDT',
      status: TransactionStatus.FAILED,
      isHeld: false
    }
  ],
  sendReceiveTransactions: [
    {
      id: 'ZK...BP',
      time: '00:12',
      from: '<EMAIL>',
      to: '<EMAIL>',
      amount: 0.002,
      currency: 'BTC',
      isHeld: true
    },
    {
      id: 'ZK...BP',
      time: '07:35', 
      from: '<EMAIL>',
      to: '<EMAIL>',
      amount: 0.002,
      currency: 'ETH',
      isHeld: false
    },
    {
      id: 'ZK...BP',
      time: '22:10',
      from: '<EMAIL>',
      to: '<EMAIL>', 
      amount: 1.55682345,
      currency: 'SOL',
      isHeld: false
    },
  ],
  exchangesTransactions: [
    {
      id: 'ZK...BP',
      time: '00:12',
      from: 'Bitcoin (BTC)',
      to: 'Tether (USDT)',
      amount: 0.002,
      currency: 'BTC',
      isHeld: true
    },
    {
      id: 'ZK...BP', 
      time: '07:35',
      from: 'Tether (USDT)',
      to: 'Bitcoin (BTC)',
      amount: 256,
      currency: 'USDT',
      isHeld: false
    },
    {
      id: 'ZK...BP',
      time: '22:10',
      from: 'Bitcoin (BTC)',
      to: 'Solana (SOL)',
      amount: 0.002,
      currency: 'BTC',
      isHeld: false
    },
  ],
  savingsTransactions: [
    {
      id: 'ZK...BP',
      time: '00:12',
      user: '<EMAIL>',
      action: SavingsAction.SAVE,
      amount: 0.002,
      currency: 'BTC'
    },
    {
      id: 'ZK...BP',
      time: '07:35',
      user: '<EMAIL>', 
      action: SavingsAction.SAVE,
      amount: 256,
      currency: 'USDT'
    },
    {
      id: 'ZK...BP',
      time: '22:10',
      user: '<EMAIL>',
      action: SavingsAction.UNSAVE,
      amount: 0.002,
      currency: 'BTC'
    },
  ],
  referralsTransactions: [
    {
      id: 'ZK...BP',
      time: '00:12',
      user: '<EMAIL>',
      referral: SavingsAction.SAVE,
      amount: 0.002,
      currency: 'BTC'
    },
    {
      id: 'ZK...BP',
      time: '07:35',
      user: '<EMAIL>',
      referral: SavingsAction.SAVE,
      amount: 256,
      currency: 'USDT'
    },
  ],
  insights: [
    {
      type: InsightType.LOAN,
      highest: { value: 0.0125, currency: 'BTC' },
      lowest: { value: 0.0125, currency: 'BTC' }
    },
    {
      type: InsightType.SAVINGS, 
      highest: { value: 0.0125, currency: 'BTC' },
      lowest: { value: 0.0125, currency: 'BTC' }
    },
    {
      type: InsightType.EXCHANGES,
      highest: { value: 0.0125, currency: 'BTC' },
      lowest: { value: 0.0125, currency: 'BTC' }
    }
  ],
  serverStatus: {
    mainServer: { responseTime: 0.9, status: 'active' },
    backupServer: { responseTime: 0.1, status: 'active' }
  }
};