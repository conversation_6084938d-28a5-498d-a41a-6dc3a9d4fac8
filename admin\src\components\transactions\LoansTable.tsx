"use client";

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Checkbox } from "@/components/ui/checkbox";
import { StatusBadge } from "./StatusBadge";
import { TransactionStatus } from "@/types/enums";
import { formatBTCAmount, formatUSDTAmount } from "@/utils/formatters";

interface LoansTransaction {
  id: string;
  time: string;
  collateral: number;
  collateralCurrency: string;
  loan: number;
  loanCurrency: string;
  status: TransactionStatus;
  isHeld: boolean;
}

interface LoansTableProps {
  transactions: LoansTransaction[];
  selectedTransactions: string[];
  onTransactionSelect: (id: string, checked: boolean) => void;
}

export function LoansTable({ 
  transactions, 
  selectedTransactions, 
  onTransactionSelect 
}: LoansTableProps) {
  return (
    <div className="w-full">
      <Table>
        <TableHeader>
          <TableRow className="border-b border-border">
            <TableHead className="text-muted-foreground font-medium">Time</TableHead>
            <TableHead className="text-muted-foreground font-medium">ID</TableHead>
            <TableHead className="text-muted-foreground font-medium">Collateral</TableHead>
            <TableHead className="text-muted-foreground font-medium">Loan</TableHead>
            <TableHead className="text-muted-foreground font-medium">Status</TableHead>
            <TableHead className="text-muted-foreground font-medium">Hold</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {transactions.map((transaction, index) => (
            <TableRow key={`${transaction.id}-${index}`}>
              <TableCell className="font-medium">{transaction.time}</TableCell>
              <TableCell>{transaction.id}</TableCell>
              <TableCell>
                {formatBTCAmount(transaction.collateral)}
              </TableCell>
              <TableCell>
                {formatUSDTAmount(transaction.loan)}
              </TableCell>
              <TableCell>
                <StatusBadge status={transaction.status} />
              </TableCell>
              <TableCell>
                <Checkbox
                  checked={selectedTransactions.includes(`${transaction.id}-${index}`)}
                  onCheckedChange={(checked) => 
                    onTransactionSelect(`${transaction.id}-${index}`, !!checked)
                  }
                />
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
}