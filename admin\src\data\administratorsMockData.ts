import { AdministratorStatus } from '../types/enums';

// Mock data for administrators page
export const mockAdministrators = [
  {
    id: '1',
    name: '<PERSON> (<PERSON>)',
    presence: 99,
    status: AdministratorStatus.ONLINE,
    isCurrentUser: true,
    avatar: 'https://i.pravatar.cc/40?img=1'
  },
  {
    id: '2', 
    name: '<PERSON>',
    presence: 99,
    status: AdministratorStatus.ONLINE,
    isCurrentUser: false,
    avatar: 'https://i.pravatar.cc/40?img=2'
  },
  {
    id: '3',
    name: '<PERSON>', 
    presence: 100,
    status: AdministratorStatus.OFFLINE,
    isCurrentUser: false,
    avatar: 'https://i.pravatar.cc/40?img=3'
  },
  {
    id: '4',
    name: '<PERSON>',
    presence: 83,
    status: AdministratorStatus.ONLINE,
    isCurrentUser: false,
    avatar: 'https://i.pravatar.cc/40?img=4'
  },
  {
    id: '5',
    name: '<PERSON>',
    presence: 87,
    status: AdministratorStatus.OFFLINE,
    isCurrentUser: false,
    avatar: 'https://i.pravatar.cc/40?img=5'
  },
  {
    id: '6',
    name: '<PERSON>',
    presence: 94,
    status: AdministratorStatus.OFFLINE,
    isCurrentUser: false,
    avatar: 'https://i.pravatar.cc/40?img=6'
  },
  {
    id: '7',
    name: 'Matthew Holmes',
    presence: 97,
    status: AdministratorStatus.OFFLINE,
    isCurrentUser: false,
    avatar: 'https://i.pravatar.cc/40?img=7'
  },
  {
    id: '8',
    name: 'George Weber',
    presence: 78,
    status: AdministratorStatus.OFFLINE,
    isCurrentUser: false,
    avatar: 'https://i.pravatar.cc/40?img=8'
  },
  {
    id: '9',
    name: 'Kelly Valdez',
    presence: 81,
    status: AdministratorStatus.OFFLINE,
    isCurrentUser: false,
    avatar: 'https://i.pravatar.cc/40?img=9'
  },
  {
    id: '10',
    name: 'Rebecca Lawson',
    presence: 89,
    status: AdministratorStatus.OFFLINE,
    isCurrentUser: false,
    avatar: 'https://i.pravatar.cc/40?img=10'
  }
];

export const mockChatMessages = [
  {
    id: '1',
    sender: 'George Pearson',
    message: 'What are you guys doing?',
    timestamp: new Date('2025-05-29T12:06:00'),
    avatar: 'https://i.pravatar.cc/40?img=11'
  },
  {
    id: '2',
    sender: 'Kathy Brewer',
    message: 'What are you guys doing?',
    timestamp: new Date('2025-05-29T12:40:00'),
    avatar: 'https://i.pravatar.cc/40?img=4'
  },
  {
    id: '3',
    sender: 'Timothy Lucas',
    message: 'What are you guys doing?',
    timestamp: new Date('2025-01-15T14:06:00'),
    avatar: 'https://i.pravatar.cc/40?img=12'
  },
  {
    id: '4',
    sender: 'Timothy Lucas',
    message: 'Are you sure?',
    timestamp: new Date('2025-01-15T14:06:00'),
    avatar: 'https://i.pravatar.cc/40?img=12'
  },
  {
    id: '5',
    sender: 'Timothy Lucas',
    message: 'This is absurd!',
    timestamp: new Date('2025-01-15T14:06:00'),
    avatar: 'https://i.pravatar.cc/40?img=12'
  },
  {
    id: '6',
    sender: 'Jeremy Powell',
    message: 'Amazing',
    timestamp: new Date('2025-01-15T13:06:00'),
    avatar: 'https://i.pravatar.cc/40?img=13'
  },
  {
    id: '7',
    sender: 'Patricia Ford',
    message: 'Hello!',
    timestamp: new Date('2025-01-15T13:06:00'),
    avatar: 'https://i.pravatar.cc/40?img=14'
  },
  {
    id: '8',
    sender: 'Elisa Gray',
    message: 'What are you guys doing?',
    timestamp: new Date('2025-01-15T12:06:00'),
    avatar: 'https://i.pravatar.cc/40?img=15'
  }
];