"use client";

import { useEffect } from "react";
import { useRouter } from "next/navigation";
import { useAppContext } from "@/hooks/context/useAppContext";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";

/**
 * Simple Admin Auth page implementing login/logout using the app context.
 * - If already authenticated, redirect to admin home (/).
 * - Provides a "Admin Dev Quick Login" that uses the existing devQuickLogin action.
 * - Provides a "Logout" when already authenticated.
 */
export default function AdminAuthPage() {
  const app = useAppContext();
  const router = useRouter();

  // If user is already authenticated, send them away from /auth
  useEffect(() => {
    if (app.isAuthenticated) {
      router.replace("/");
    }
  }, [app.isAuthenticated, router]);

  return (
    <div className="min-h-[60vh] flex items-center justify-center p-6">
      <Card className="w-full max-w-sm p-6 space-y-4">
        <h1 className="text-xl font-semibold">Admin Authentication</h1>

        {!app.isAuthenticated ? (
          <div className="space-y-3">
            <p className="text-sm text-muted-foreground">Admin login required.</p>
            <Button
              className="w-full"
              onClick={() => {
                app.setDevMode(true);
                app.devQuickLogin();
                // After login, route home
                router.replace("/");
              }}
            >
              Admin Dev Login
            </Button>
          </div>
        ) : (
          <div className="space-y-3">
            <p className="text-sm">You are logged in{app.user ? ` as ${app.user.name}` : ""}.</p>
            <Button
              variant="secondary"
              className="w-full"
              onClick={() => {
                app.devQuickLogout();
                // After logout, remain on /auth (guard will also keep you here)
                router.replace("/auth");
              }}
            >
              Admin Logout
            </Button>
          </div>
        )}
      </Card>
    </div>
  );
}
