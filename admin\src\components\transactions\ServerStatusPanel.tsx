"use client";

import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "@/components/ui/card";
import { formatServerResponseTime } from "@/utils/formatters";

interface ServerStatus {
  mainServer: { responseTime: number; status: string };
  backupServer: { responseTime: number; status: string };
}

interface ServerStatusPanelProps {
  serverStatus: ServerStatus;
}

export function ServerStatusPanel({ serverStatus }: ServerStatusPanelProps) {
  const getStatusColor = (status: string) => {
    return status === 'active' ? 'bg-orange-500' : 'bg-gray-400';
  };

  const getResponseTimeColor = (time: number) => {
    if (time < 1) return 'text-blue-600';
    if (time < 2) return 'text-orange-600';
    return 'text-red-600';
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg font-semibold">Server status</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <div className={`w-2 h-2 rounded-full ${getStatusColor(serverStatus.mainServer.status)}`} />
            <span className="text-sm font-medium">Main server</span>
          </div>
          <span className={`text-sm font-medium ${getResponseTimeColor(serverStatus.mainServer.responseTime)}`}>
            {formatServerResponseTime(serverStatus.mainServer.responseTime)}
          </span>
        </div>
        
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <div className={`w-2 h-2 rounded-full ${getStatusColor(serverStatus.backupServer.status)}`} />
            <span className="text-sm font-medium">Backup server</span>
          </div>
          <span className={`text-sm font-medium ${getResponseTimeColor(serverStatus.backupServer.responseTime)}`}>
            {formatServerResponseTime(serverStatus.backupServer.responseTime)}
          </span>
        </div>
      </CardContent>
    </Card>
  );
}