"use client";

import { Badge } from "@/components/ui/badge";
import { UserTier } from "@/app/userManagementMockData";

interface TierBadgeProps {
  tier: UserTier;
}

export function TierBadge({ tier }: TierBadgeProps) {
  const isPro = tier === UserTier.PRO;
  
  return (
    <Badge 
      variant={isPro ? "default" : "secondary"}
      className={`px-2 py-1 text-xs font-medium ${
        isPro 
          ? "bg-pro-badge text-white hover:bg-pro-badge/90" 
          : "bg-reg-badge text-white hover:bg-reg-badge/90"
      }`}
    >
      {tier}
    </Badge>
  );
}