"use client";

import { Chart<PERSON>ontainer, ChartTooltip, ChartTooltipContent } from "@/components/ui/chart";
import { LineC<PERSON>, Line, XAxis, YAxis } from "recharts";
import { PeriodFilterSidebar } from "./PeriodFilter";

interface PortfolioData {
  month: string;
  value: number;
}

interface PortfolioInsightsChartProps {
  data: PortfolioData[];
  selectedPeriod: string;
  onPeriodChange: (period: string) => void;
  chartConfig: any;
}

export function PortfolioInsightsChart({
  data,
  selectedPeriod,
  onPeriodChange,
  chartConfig
}: PortfolioInsightsChartProps) {
  return (
    <div>
      <h3 className="mb-4 text-gray-900 text-2xl">Portfolio insights</h3>
      <div className="rounded-2xl bg-[#F8F8F8] p-6">
        <div className="mb-4">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm text-gray-600 font-semibold">Interest earned all time</span>
            <PeriodFilterSidebar
              selectedPeriod={selectedPeriod}
              onPeriodChange={onPeriodChange}
            />
          </div>
        </div>

        <div className="h-[70%]">
          <ChartContainer config={chartConfig} className="w-full h-full p-0">
              <LineChart data={data} margin={{ top: 10, right: 10, left: -30, bottom: 0 }}>
                <XAxis dataKey="month" axisLine={false} tickLine={false}/>
                <YAxis axisLine={false} tickLine={false} />
                <ChartTooltip content={<ChartTooltipContent />} />
                <Line
                  dataKey="value"
                  stroke="#22c55e"
                  strokeWidth={2}
                  dot={false}
                />
              </LineChart>
          </ChartContainer>
        </div>
      </div>
    </div>
  );
}