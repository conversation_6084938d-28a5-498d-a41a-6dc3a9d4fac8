"use client";

import { <PERSON>, CardHeader, CardContent } from "@/components/ui/card";
import { AdministratorsTable } from "@/components/administrators/AdministratorsTable";
import { ChatWindow } from "@/components/administrators/ChatWindow";
import { mockAdministrators, mockChatMessages } from "@/data/administratorsMockData";

export default function AdministratorsPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      <div className="h-full grid grid-cols-1 gap-6 xl:grid-cols-[6fr_4fr] p-6">
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <h1 className="text-2xl font-bold">Administrators</h1>
            </CardHeader>
            <CardContent>
              <AdministratorsTable administrators={mockAdministrators} />
            </CardContent>
          </Card>
        </div>

        <div className="h-[calc(100vh-3rem)]">
          <ChatWindow messages={mockChatMessages} />
        </div>
      </div>
    </div>
  );
}