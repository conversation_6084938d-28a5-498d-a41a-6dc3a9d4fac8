"use client";

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { InsightType } from "@/types/enums";
import { formatCryptoAmount } from "@/utils/formatters";

interface InsightData {
  type: InsightType;
  highest: { value: number; currency: string };
  lowest: { value: number; currency: string };
}

interface InsightsPanelProps {
  insights: InsightData[];
}

export function InsightsPanel({ insights }: InsightsPanelProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg font-semibold flex items-center justify-between">
          Insights
          <div className="flex items-center gap-4 text-sm">
            <span className="text-blue-600">Highest</span>
            <span className="text-muted-foreground">Lowest</span>
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {insights.map((insight) => (
          <div key={insight.type} className="flex items-center justify-between">
            <span className="text-sm font-medium">{insight.type}</span>
            <div className="flex items-center gap-4 text-sm">
              <span className="text-blue-600">
                {formatCryptoAmount(insight.highest.value, insight.highest.currency)}
              </span>
              <span className="text-muted-foreground">
                {formatCryptoAmount(insight.lowest.value, insight.lowest.currency)}
              </span>
            </div>
          </div>
        ))}
      </CardContent>
    </Card>
  );
}