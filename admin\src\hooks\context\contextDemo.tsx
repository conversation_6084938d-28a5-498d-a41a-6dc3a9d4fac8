"use client";

import React from "react";
import { useAppContext } from "./useAppContext";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";

/**
 * Admin developer panel: only Login and Logout for admin.
 */
export default function ContextDemo() {
  const app = useAppContext();

  return (
    <div className="p-2">
      <Card className="p-3 flex items-center justify-between gap-3">
        <div className="text-sm">
          {app.isAuthenticated ? (
            <>
              <span className="font-medium">Admin Logged in</span>
              {app.user ? ` as ${app.user.name} (${app.user.role})` : null}
            </>
          ) : (
            <span className="font-medium">Admin Logged out</span>
          )}
        </div>

        {!app.isAuthenticated ? (
          <Button
            size="sm"
            onClick={() => {
              app.setDevMode(true);
              app.devQuickLogin();
            }}
          >
            Admin <PERSON>gin
          </Button>
        ) : (
          <Button size="sm" variant="secondary" onClick={() => app.devQuickLogout()}>
            Admin Logout
          </Button>
        )}
      </Card>
    </div>
  );
}
