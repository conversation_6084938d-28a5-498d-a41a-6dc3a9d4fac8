// Enums for the admin dashboard
export enum TimePeriod {
  DAY = 'D',
  WEEK = 'W', 
  MONTH = 'M',
  SIX_MONTHS = '6M',
  YEAR_TO_DATE = 'YTD',
  ALL = 'All'
}

export enum TransactionType {
  LOANS = 'Loans',
  SENDING = 'Sending', 
  BUYING = 'Buying',
  SAVINGS = 'Savings',
  RECEIVES = 'Receives',
  SELLINGS = 'Sellings'
}

export enum CryptoCurrency {
  BITCOIN = 'Bitcoin',
  ETHEREUM = 'Ethereum',
  SOLANA = 'Solana', 
  BNB = 'BNB',
  DASH = 'Dash',
  AVALANCHE = 'Avalanche',
  SHIBA_INU = 'Shiba Inu',
  PEPE = 'PEPE',
  XRP = 'XRP'
}

// Transaction-specific enums
export enum TransactionStatus {
  PROCESSING = 'Processing',
  COMPLETED = 'Completed', 
  FAILED = 'Failed'
}

export enum TransactionCategory {
  LOANS = 'Loans',
  SEND_RECEIVE = 'Send/Receive',
  EXCHANGES = 'Exchanges', 
  SAVINGS = 'Savings',
  REFERRALS = 'Referrals'
}

export enum SavingsAction {
  SAVE = 'Save',
  UNSAVE = 'Unsave'
}

export enum InsightType {
  LOAN = 'Loan',
  SAVINGS = 'Savings',
  EXCHANGES = 'Exchanges'
}

// Administrator status and presence enums
export enum AdministratorStatus {
  ONLINE = 'Online',
  OFFLINE = 'Offline'
}

export enum PresenceLevel {
  EXCELLENT = 'excellent', // 95%+
  GOOD = 'good', // 85-94%
  AVERAGE = 'average', // 70-84%
  POOR = 'poor' // <70%
}