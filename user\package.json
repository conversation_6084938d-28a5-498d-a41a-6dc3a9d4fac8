{"name": "0f9bc1939b26311fb92cf950714089cc0f6c0e7b1571723e283982c7435cc61e", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "preview": "next start"}, "dependencies": {"@kombai/react-error-boundary": "^1.1.0", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-aspect-ratio": "^1.1.7", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-context-menu": "^2.2.15", "@radix-ui/react-dialog": "^1.1.15", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-hover-card": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-menubar": "^1.1.15", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.6", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "@thirdweb-dev/chain-icons": "^1.0.5", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "embla-carousel-react": "^8.6.0", "input-otp": "^1.4.2", "lucide-react": "^0.536.0", "next": "15.4.6", "next-themes": "^0.4.6", "react": "19.1.0", "react-day-picker": "^9.8.1", "react-dom": "19.1.0", "react-icons": "^5.5.0", "react-resizable-panels": "^3.0.4", "recharts": "^2.15.4", "shadcn": "^2.10.0", "sonner": "^2.0.7", "tailwind-merge": "^3.3.1", "vaul": "^1.1.2", "zustand": "^5.0.7"}, "devDependencies": {"@tailwindcss/postcss": "^4.1.11", "@types/node": "^20.19.9", "@types/react": "^19", "@types/react-dom": "^19", "comment-json": "^4.2.5", "env-cmd": "^10.1.0", "postcss": "^8.5.6", "postcss-import": "^16.1.1", "tailwindcss": "^4.1.11", "ts-morph": "^26.0.0", "tw-animate-css": "^1.3.6", "typescript": "^5", "webpack": "^5.101.0"}, "type": "module", "strictDependencies": {"@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-tooltip": "^1.2.7", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.536.0", "next": "15.4.5", "react": "19.1.0", "react-dom": "19.1.0", "recharts": "2.15.4", "tailwind-merge": "^3.3.1", "zustand": "^5.0.7", "@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.4.5", "tailwindcss": "^4", "tw-animate-css": "^1.3.6", "typescript": "^5"}}