"use client";

import { useState } from "react";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { TimePeriodFilter } from "@/components/dashboard/TimePeriodFilter";
import { UserProfile } from "@/components/dashboard/UserProfile";
import { EstimatedProfitsChart } from "@/components/dashboard/EstimatedProfitsChart";
import { TransactionsVolumeChart } from "@/components/dashboard/TransactionsVolumeChart";
import { CollateralsInsightPanel } from "@/components/dashboard/CollateralsInsightPanel";
import { TopRequestedCoins } from "@/components/dashboard/TopRequestedCoins";
import { mockRootProps } from "@/data/dashboardMockData";
import { TimePeriod } from "@/types/enums";
import { formatCurrency } from "@/utils/formatters";
import { Download } from "lucide-react";

export default function DashboardPreviewPage() {
  const [selectedTimePeriod, setSelectedTimePeriod] = useState<TimePeriod>(mockRootProps.selectedTimePeriod);

  return (
    <div className="w-full min-h-screen p-6 bg-gray-50">
      <div className="h-full grid grid-cols-1 gap-6 xl:grid-cols-[2fr_1fr]">
        {/* Main Content */}
        <div className="w-full min-h-full flex flex-col gap-6">
          {/* Header Section */}
          <Card className="p-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-8">
                <div>
                  <div className="text-sm text-gray-500 mb-1">Estimated profits</div>
                  <div className="text-3xl font-bold text-gray-900">
                    {formatCurrency(mockRootProps.estimatedProfits)}
                  </div>
                </div>
                <Button variant="outline" size="sm" className="text-blue-600 border-blue-600 hover:bg-blue-50">
                  <Download size={16} className="mr-2" />
                  Export
                </Button>
              </div>
              
              <div className="flex items-center gap-6">
                <TimePeriodFilter 
                  selectedPeriod={selectedTimePeriod}
                  onPeriodChange={setSelectedTimePeriod}
                />
                <UserProfile user={mockRootProps.user} />
              </div>
            </div>
          </Card>

          {/* Estimated Profits Chart */}
          <Card className="p-6 flex-1">
            <div className="h-80">
              <EstimatedProfitsChart data={mockRootProps.profitsChartData} />
            </div>
          </Card>

          {/* Transactions Volume Chart */}
          <Card className="p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900">Transactions volume</h3>
              <Button variant="outline" size="sm" className="text-blue-600 border-blue-600 hover:bg-blue-50">
                <Download size={16} className="mr-2" />
                Export
              </Button>
            </div>
            <div className="flex items-center gap-6 mb-4">
              <TimePeriodFilter 
                selectedPeriod={selectedTimePeriod}
                onPeriodChange={setSelectedTimePeriod}
              />
            </div>
            <div className="h-80">
              <TransactionsVolumeChart data={mockRootProps.transactionsData} />
            </div>
          </Card>
        </div>

        {/* Right Sidebar */}
        <div className="w-full min-h-full flex flex-col gap-6">
          <CollateralsInsightPanel data={mockRootProps.collateralsData} />
          <TopRequestedCoins coins={mockRootProps.topRequestedCoins} />
        </div>
      </div>
    </div>
  );
}