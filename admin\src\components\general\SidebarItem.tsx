"use client";

import Link from "next/link";
import React from "react";
import clsx from "clsx";

type Props = {
  href: string;
  label: string;
  icon: React.ReactNode;
  trailing?: React.ReactNode;
  active?: boolean;
  onClick?: () => void;
};

const SidebarItem: React.FC<Props> = ({ href, label, icon, trailing, active, onClick }) => {
  const handleClick = (e: React.MouseEvent) => {
    if (onClick) {
      e.preventDefault();
      onClick();
    }
  };

  return (
    <Link
      href={href}
      onClick={handleClick}
      className={clsx(
        "group flex items-center justify-between gap-3 xl:pr-6",
        "text-slate-600 hover:text-slate-900",
        "transition-colors"
      )}
    >
      <div className="flex items-center xl:gap-4 gap-1">
        <div
          className={clsx(
            "grid place-items-center rounded-full",
            "h-10 w-10",
            active ? "bg-[#2563EB]" : "bg-slate-700"
          )}
        >
          {icon}
        </div>
        <span className={clsx("text-2xl", active ? "text-[#2563EB] font-semibold" : "text-slate-700")}>
          {label}
        </span>
      </div>
      {active ? (
        <div className="opacity-100">
          <svg width="12" height="12" viewBox="0 0 12 12" fill="none">
            <path d="M4 2L8 6L4 10" stroke="#2563EB" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
          </svg>
        </div>
      ) : trailing ? (
        <div className="opacity-100">{trailing}</div>
      ) : null}
    </Link>
  );
};

export default SidebarItem;
