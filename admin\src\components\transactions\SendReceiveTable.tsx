"use client";

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Checkbox } from "@/components/ui/checkbox";
import { formatCryptoAmount } from "@/utils/formatters";

interface SendReceiveTransaction {
  id: string;
  time: string;
  from: string;
  to: string;
  amount: number;
  currency: string;
  isHeld: boolean;
}

interface SendReceiveTableProps {
  transactions: SendReceiveTransaction[];
  selectedTransactions: string[];
  onTransactionSelect: (id: string, checked: boolean) => void;
}

export function SendReceiveTable({ 
  transactions, 
  selectedTransactions, 
  onTransactionSelect 
}: SendReceiveTableProps) {
  const getAmountColor = (currency: string) => {
    switch (currency) {
      case 'BTC':
        return 'text-orange-600';
      case 'ETH':
        return 'text-orange-600';
      case 'SOL':
        return 'text-green-600';
      default:
        return 'text-foreground';
    }
  };

  return (
    <div className="w-full">
      <Table>
        <TableHeader>
          <TableRow className="border-b border-border">
            <TableHead className="text-muted-foreground font-medium">Time</TableHead>
            <TableHead className="text-muted-foreground font-medium">ID</TableHead>
            <TableHead className="text-muted-foreground font-medium">From</TableHead>
            <TableHead className="text-muted-foreground font-medium">To</TableHead>
            <TableHead className="text-muted-foreground font-medium">Amount</TableHead>
            <TableHead className="text-muted-foreground font-medium">Hold</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {transactions.map((transaction, index) => (
            <TableRow key={`${transaction.id}-${index}`}>
              <TableCell className="font-medium">{transaction.time}</TableCell>
              <TableCell>{transaction.id}</TableCell>
              <TableCell>{transaction.from}</TableCell>
              <TableCell>{transaction.to}</TableCell>
              <TableCell className={getAmountColor(transaction.currency)}>
                {formatCryptoAmount(transaction.amount, transaction.currency)}
              </TableCell>
              <TableCell>
                <Checkbox
                  checked={selectedTransactions.includes(`${transaction.id}-${index}`)}
                  onCheckedChange={(checked) => 
                    onTransactionSelect(`${transaction.id}-${index}`, !!checked)
                  }
                />
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
}