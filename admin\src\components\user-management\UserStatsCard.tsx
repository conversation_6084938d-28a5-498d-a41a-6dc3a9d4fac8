"use client";

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { Star } from "lucide-react";

interface UserStats {
  totalUsers: number;
  activeUsers: number;
  inactiveUsers: number;
  highestBalance: string;
  topReferral: string;
  topSavings: string;
  highestLoan: string;
  proAccounts: number;
  highestAssetsValue: string;
}

interface UserStatsCardProps {
  stats: UserStats;
}

export function UserStatsCard({ stats }: UserStatsCardProps) {
  const formatNumber = (num: number): string => {
    return num.toLocaleString();
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg font-semibold">User statistics</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <span className="text-sm text-muted-foreground">Total users</span>
            <span className="font-semibold">{formatNumber(stats.totalUsers)}</span>
          </div>
          
          <div className="flex items-center gap-2">
            <div className="flex-1 bg-lendbloc-blue h-2 rounded-full"></div>
            <div className="w-8 bg-red-500 h-2 rounded-full"></div>
          </div>
          
          <div className="flex items-center justify-between text-xs">
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-lendbloc-blue rounded-full"></div>
              <span className="text-lendbloc-blue font-medium">{formatNumber(stats.activeUsers)} Active</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-red-500 rounded-full"></div>
              <span className="text-red-500 font-medium">{formatNumber(stats.inactiveUsers)} Inactive</span>
            </div>
          </div>
        </div>

        <Separator />

        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <span className="text-sm text-muted-foreground">Highest balance</span>
            <div className="flex items-center gap-1">
              <Star size={12} className="text-pro-badge fill-pro-badge" />
              <span className="text-sm font-medium">{stats.highestBalance}</span>
            </div>
          </div>
          
          <div className="flex items-center justify-between">
            <span className="text-sm text-muted-foreground">Top referral</span>
            <span className="text-sm font-medium">{stats.topReferral}</span>
          </div>
          
          <div className="flex items-center justify-between">
            <span className="text-sm text-muted-foreground">Top savings</span>
            <span className="text-sm font-medium">{stats.topSavings}</span>
          </div>
          
          <div className="flex items-center justify-between">
            <span className="text-sm text-muted-foreground">Highest loan</span>
            <span className="text-sm font-medium">{stats.highestLoan}</span>
          </div>
          
          <div className="flex items-center justify-between">
            <span className="text-sm text-muted-foreground">PRO Account</span>
            <span className="text-sm font-medium text-pro-badge">{stats.proAccounts} Accounts</span>
          </div>
          
          <div className="flex items-center justify-between">
            <span className="text-sm text-muted-foreground">Highest assets value</span>
            <span className="text-sm font-medium">{stats.highestAssetsValue}</span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}