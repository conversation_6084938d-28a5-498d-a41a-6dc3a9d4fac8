"use client";

import { TrendingUp, TrendingDown } from "lucide-react";
import { formatPercentage } from "@/utils/formatters";

interface TrendIndicatorProps {
  change: number;
  trend: 'up' | 'down';
}

export function TrendIndicator({ change, trend }: TrendIndicatorProps) {
  const isPositive = trend === 'up';
  
  return (
    <div className={`flex items-center gap-1 ${isPositive ? 'text-green-600' : 'text-red-600'}`}>
      {isPositive ? (
        <TrendingUp size={16} />
      ) : (
        <TrendingDown size={16} />
      )}
      <span className="font-medium">{formatPercentage(Math.abs(change))}</span>
    </div>
  );
}