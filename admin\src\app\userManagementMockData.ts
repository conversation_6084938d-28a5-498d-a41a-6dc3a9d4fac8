// User tier types
export enum UserTier {
  PRO = "PRO",
  REG = "REG"
}

// Chart time period options
export enum ChartPeriod {
  WEEK = "W",
  MONTH = "M", 
  YEAR = "Y",
  YEAR_TO_DATE = "YTD"
}

// User status types
export enum UserStatus {
  ACTIVE = "Active",
  INACTIVE = "Inactive"
}

// Data for global state store
export const mockStore = {
  selectedPeriod: ChartPeriod.WEEK as const,
  searchQuery: "",
  selectedUser: null
};

// Data returned by API queries
export const mockQuery = {
  users: [
    {
      id: "1",
      email: "<EMAIL>",
      balance: 35256.00,
      tier: UserTier.PRO as const,
      loan: 2000.34,
      isActive: true
    },
    {
      id: "2", 
      email: "<EMAIL>",
      balance: 2486.03,
      tier: UserTier.REG as const,
      loan: 214.00,
      isActive: true
    },
    {
      id: "3",
      email: "<EMAIL>", 
      balance: 12.03,
      tier: UserTier.REG as const,
      loan: 25.00,
      isActive: false
    },
    {
      id: "4",
      email: "<EMAIL>",
      balance: 317.35,
      tier: UserTier.REG as const,
      loan: 214.00,
      isActive: true
    },
    {
      id: "5",
      email: "<EMAIL>",
      balance: 2486.03,
      tier: UserTier.REG as const,
      loan: 214.00,
      isActive: true
    },
    {
      id: "6",
      email: "<EMAIL>",
      balance: 12.03,
      tier: UserTier.REG as const,
      loan: 25.00,
      isActive: false
    },
    {
      id: "7",
      email: "<EMAIL>",
      balance: 12.03,
      tier: UserTier.REG as const,
      loan: 25.00,
      isActive: true
    },
    {
      id: "8",
      email: "<EMAIL>",
      balance: 317.35,
      tier: UserTier.REG as const,
      loan: 2000.34,
      isActive: true
    },
    {
      id: "9",
      email: "<EMAIL>",
      balance: 12.03,
      tier: UserTier.REG as const,
      loan: 214.00,
      isActive: false
    },
    {
      id: "10",
      email: "<EMAIL>",
      balance: 614.36,
      tier: UserTier.REG as const,
      loan: 2000.34,
      isActive: true
    }
  ],
  chartData: [
    { day: "Mo", value: 25 },
    { day: "Tue", value: 35 },
    { day: "Wed", value: 28 },
    { day: "Thu", value: 42 },
    { day: "Fri", value: 38 },
    { day: "Sat", value: 48 },
    { day: "Sun", value: 45 }
  ],
  userStats: {
    totalUsers: 8982,
    activeUsers: 8000,
    inactiveUsers: 982,
    highestBalance: "<EMAIL>",
    topReferral: "<EMAIL>", 
    topSavings: "<EMAIL>",
    highestLoan: "<EMAIL>",
    proAccounts: 78,
    highestAssetsValue: "<EMAIL>"
  }
};

// Data passed as props to the root component
export const mockRootProps = {
  initialPeriod: ChartPeriod.WEEK as const
};