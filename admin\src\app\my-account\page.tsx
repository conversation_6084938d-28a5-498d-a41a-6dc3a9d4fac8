"use client";

import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { ChatWindow } from "@/components/administrators/ChatWindow";
import { UserProfileHeader } from "@/components/my-account/UserProfileHeader";
import { EditableField } from "@/components/my-account/EditableField";
import { PhoneNumberInput } from "@/components/my-account/PhoneNumberInput";
import { TwoFactorStatus } from "@/components/my-account/TwoFactorStatus";
import { PresenceLogsTable } from "@/components/my-account/PresenceLogsTable";
import { mockAccountData, mockChatMessages } from "@/data/myAccountMockData";

export default function MyAccountPage() {
  const handleUpdateEmail = (email: string) => {
    console.log("Updating email:", email);
  };

  const handleUpdatePassword = (password: string) => {
    console.log("Updating password:", password);
  };

  const handleUpdatePhone = (countryCode: string, number: string) => {
    console.log("Updating phone:", countryCode, number);
  };

  const handleToggle2FA = () => {
    console.log("Toggling 2FA");
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="h-full grid grid-cols-1 gap-6 xl:grid-cols-[6fr_4fr] p-6">
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <UserProfileHeader user={mockAccountData.user} />
            </CardHeader>
            <CardContent className="space-y-6">
              <EditableField
                label="Email address"
                value={mockAccountData.user.email}
                type="email"
                onSave={handleUpdateEmail}
                placeholder="Enter email address"
              />
              
              <Separator />
              
              <PhoneNumberInput
                countryCode={mockAccountData.user.phone.countryCode}
                number={mockAccountData.user.phone.number}
                onSubmit={handleUpdatePhone}
              />
              
              <Separator />
              
              <EditableField
                label="Password"
                value="currentpassword"
                type="password"
                onSave={handleUpdatePassword}
                placeholder="Enter new password"
              />
              
              <Separator />
              
              <TwoFactorStatus
                isEnabled={mockAccountData.user.twoFactorEnabled}
                onToggle={handleToggle2FA}
              />
              
              <Separator />
              
              <PresenceLogsTable logs={mockAccountData.presenceLogs} />
            </CardContent>
          </Card>
        </div>

        <div className="h-[calc(100vh-3rem)]">
          <ChatWindow messages={mockChatMessages} />
        </div>
      </div>
    </div>
  );
}