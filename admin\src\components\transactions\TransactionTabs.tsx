"use client";

import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { TransactionCategory } from "@/types/enums";
import { LoansTable } from "./LoansTable";
import { SendReceiveTable } from "./SendReceiveTable";
import { ExchangesTable } from "./ExchangesTable";
import { SavingsTable } from "./SavingsTable";
import { ReferralsTable } from "./ReferralsTable";
import { useState } from "react";

interface TransactionTabsProps {
  selectedTab: TransactionCategory;
  onTabChange: (tab: TransactionCategory) => void;
  loansData: any[];
  sendReceiveData: any[];
  exchangesData: any[];
  savingsData: any[];
  referralsData: any[];
}

export function TransactionTabs({
  selectedTab,
  onTabChange,
  loansData,
  sendReceiveData,
  exchangesData,
  savingsData,
  referralsData
}: TransactionTabsProps) {
  const [selectedTransactions, setSelectedTransactions] = useState<string[]>([]);

  const handleTransactionSelect = (id: string, checked: boolean) => {
    if (checked) {
      setSelectedTransactions(prev => [...prev, id]);
    } else {
      setSelectedTransactions(prev => prev.filter(item => item !== id));
    }
  };

  return (
    <Tabs value={selectedTab} onValueChange={(value) => onTabChange(value as TransactionCategory)}>
      <TabsList className="grid w-full grid-cols-5 mb-6">
        <TabsTrigger 
          value={TransactionCategory.LOANS}
          className="data-[state=active]:bg-lendbloc-blue data-[state=active]:text-white"
        >
          Loans
        </TabsTrigger>
        <TabsTrigger 
          value={TransactionCategory.SEND_RECEIVE}
          className="data-[state=active]:bg-lendbloc-blue data-[state=active]:text-white"
        >
          Send/Receive
        </TabsTrigger>
        <TabsTrigger 
          value={TransactionCategory.EXCHANGES}
          className="data-[state=active]:bg-lendbloc-blue data-[state=active]:text-white"
        >
          Exchanges
        </TabsTrigger>
        <TabsTrigger 
          value={TransactionCategory.SAVINGS}
          className="data-[state=active]:bg-lendbloc-blue data-[state=active]:text-white"
        >
          Savings
        </TabsTrigger>
        <TabsTrigger 
          value={TransactionCategory.REFERRALS}
          className="data-[state=active]:bg-lendbloc-blue data-[state=active]:text-white"
        >
          Referrals
        </TabsTrigger>
      </TabsList>

      <TabsContent value={TransactionCategory.LOANS}>
        <LoansTable 
          transactions={loansData}
          selectedTransactions={selectedTransactions}
          onTransactionSelect={handleTransactionSelect}
        />
      </TabsContent>

      <TabsContent value={TransactionCategory.SEND_RECEIVE}>
        <SendReceiveTable 
          transactions={sendReceiveData}
          selectedTransactions={selectedTransactions}
          onTransactionSelect={handleTransactionSelect}
        />
      </TabsContent>

      <TabsContent value={TransactionCategory.EXCHANGES}>
        <ExchangesTable 
          transactions={exchangesData}
          selectedTransactions={selectedTransactions}
          onTransactionSelect={handleTransactionSelect}
        />
      </TabsContent>

      <TabsContent value={TransactionCategory.SAVINGS}>
        <SavingsTable transactions={savingsData} />
      </TabsContent>

      <TabsContent value={TransactionCategory.REFERRALS}>
        <ReferralsTable transactions={referralsData} />
      </TabsContent>
    </Tabs>
  );
}