"use client";

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { ArrowRight } from "lucide-react";

interface ToolsPanelProps {
  onToolClick: (tool: string) => void;
}

export function ToolsPanel({ onToolClick }: ToolsPanelProps) {
  const tools = [
    "Fee adjustment",
    "KYC Manager", 
    "Account blocker",
    "Transaction blocker"
  ];

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg font-semibold">Tools</CardTitle>
      </CardHeader>
      <CardContent className="space-y-3">
        {tools.map((tool) => (
          <Button
            key={tool}
            onClick={() => onToolClick(tool)}
            className="w-full bg-lendbloc-blue hover:bg-lendbloc-blue-dark text-white justify-between"
          >
            {tool}
            <ArrowRight size={16} />
          </Button>
        ))}
      </CardContent>
    </Card>
  );
}