"use client";

import React from 'react';
import { ArrowRight } from 'lucide-react';
import { HoldingData } from '../../types/schema';
import { CryptoCurrency } from '../../types/enums';
import { formatPortfolioValue, formatPercentageChange } from '../../utils/formatters';
import CryptoIcon from '../general/CryptoIcon';

interface CryptocurrencyHoldingProps {
  holding: HoldingData;
}


export const CryptocurrencyHolding: React.FC<CryptocurrencyHoldingProps> = ({ holding }) => {
  return (
    <div className="flex items-center justify-between p-3 bg-[#F8F8F8] hover:bg-gray-50 rounded-2xl transition-colors cursor-pointer">
      <div className="flex items-center gap-3">
        <CryptoIcon
          symbol={holding.currency}
          size={24}
          className="w-10 h-10"
        />
        <div>
          <div className="font-medium">{holding.name}</div>
          <div className={`text-sm font-medium ${holding.isPositive ? 'text-green-600' : 'text-red-600'
            }`}>
            {formatPercentageChange(holding.change)}
          </div>
        </div>
      </div>

      <div className="flex items-center gap-2">
        <ArrowRight className="w-4 h-4 text-muted-foreground" />
      </div>
    </div>
  );
};