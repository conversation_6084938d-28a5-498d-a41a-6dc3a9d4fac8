import { Badge } from "@/components/ui/badge";
import { AdministratorStatus } from "@/types/enums";

interface StatusBadgeProps {
  status: AdministratorStatus;
}

export function StatusBadge({ status }: StatusBadgeProps) {
  const isOnline = status === AdministratorStatus.ONLINE;
  
  return (
    <Badge 
      variant={isOnline ? "default" : "secondary"}
      className={`${
        isOnline 
          ? "bg-success-green text-white hover:bg-success-green/90" 
          : "bg-muted text-muted-foreground"
      }`}
    >
      {status}
    </Badge>
  );
}