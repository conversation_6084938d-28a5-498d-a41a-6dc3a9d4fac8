"use client";

import { useState } from "react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { SquarePen } from "lucide-react";

interface EditableFieldProps {
  label: string;
  value: string;
  type?: "text" | "email" | "password";
  onSave: (value: string) => void;
  placeholder?: string;
}

export function EditableField({ 
  label, 
  value, 
  type = "text", 
  onSave, 
  placeholder 
}: EditableFieldProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [editValue, setEditValue] = useState(value);

  const handleSave = () => {
    onSave(editValue);
    setIsEditing(false);
  };

  const handleCancel = () => {
    setEditValue(value);
    setIsEditing(false);
  };

  const displayValue = type === "password" ? "••••••••••" : value;

  return (
    <div className="space-y-2">
      <Label className="text-sm font-medium text-foreground">{label}</Label>
      <div className="flex items-center gap-3">
        {isEditing ? (
          <div className="flex-1 flex items-center gap-2">
            <Input
              type={type === "password" ? "password" : "text"}
              value={editValue}
              onChange={(e) => setEditValue(e.target.value)}
              placeholder={placeholder}
              className="flex-1"
            />
            <Button
              onClick={handleSave}
              size="sm"
              className="bg-lendbloc-blue hover:bg-lendbloc-blue-dark"
            >
              Save
            </Button>
            <Button
              onClick={handleCancel}
              size="sm"
              variant="outline"
            >
              Cancel
            </Button>
          </div>
        ) : (
          <>
            <div className="flex-1 py-2 px-3 bg-gray-50 rounded-md border">
              <span className="text-sm text-foreground">{displayValue}</span>
            </div>
            <Button
              onClick={() => setIsEditing(true)}
              size="sm"
              variant="outline"
              className="h-9 w-9 p-0"
            >
              <SquarePen className="h-4 w-4 text-lendbloc-blue" />
            </Button>
          </>
        )}
      </div>
    </div>
  );
}