"use client";

import { useState } from "react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

interface PhoneNumberInputProps {
  countryCode: string;
  number: string;
  onSubmit: (countryCode: string, number: string) => void;
}

const countryCodes = [
  { code: "+1", country: "US" },
  { code: "+44", country: "UK" },
  { code: "+33", country: "FR" },
  { code: "+49", country: "DE" },
  { code: "+81", country: "JP" },
  { code: "+86", country: "CN" },
  { code: "+91", country: "IN" },
];

export function PhoneNumberInput({ countryCode, number, onSubmit }: PhoneNumberInputProps) {
  const [selectedCountryCode, setSelectedCountryCode] = useState(countryCode);
  const [phoneNumber, setPhoneNumber] = useState(number);

  const handleSubmit = () => {
    if (phoneNumber.trim()) {
      onSubmit(selectedCountryCode, phoneNumber);
    }
  };

  return (
    <div className="space-y-2">
      <Label className="text-sm font-medium text-foreground">Phone Number</Label>
      <div className="flex items-center gap-2">
        <Select value={selectedCountryCode} onValueChange={setSelectedCountryCode}>
          <SelectTrigger className="w-20">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            {countryCodes.map((country) => (
              <SelectItem key={country.code} value={country.code}>
                {country.code}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        
        <Input
          type="tel"
          value={phoneNumber}
          onChange={(e) => setPhoneNumber(e.target.value)}
          placeholder="12345 678"
          className="flex-1"
        />
        
        <Button
          onClick={handleSubmit}
          className="bg-lendbloc-blue hover:bg-lendbloc-blue-dark px-6"
        >
          SUBMIT
        </Button>
      </div>
    </div>
  );
}