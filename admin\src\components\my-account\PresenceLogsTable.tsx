"use client";

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { PresenceLogEntry } from "@/types/myAccountSchema";
import { formatPresenceStatus, formatTimeLeft } from "@/utils/myAccountFormatters";

interface PresenceLogsTableProps {
  logs: PresenceLogEntry[];
}

export function PresenceLogsTable({ logs }: PresenceLogsTableProps) {
  const getStatusBadge = (log: PresenceLogEntry) => {
    if (log.status === 'active' && log.timeLeft) {
      return (
        <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
          {formatTimeLeft(log.timeLeft)}
        </Badge>
      );
    }
    
    if (log.status === 'absent') {
      return (
        <Badge variant="outline" className="bg-gray-50 text-gray-700 border-gray-200">
          Absent
        </Badge>
      );
    }
    
    if (log.percentage !== null) {
      const isExcellent = log.percentage >= 95;
      const isGood = log.percentage >= 85;
      
      return (
        <Badge 
          variant="outline" 
          className={
            isExcellent 
              ? "bg-green-50 text-green-700 border-green-200" 
              : isGood 
                ? "bg-yellow-50 text-yellow-700 border-yellow-200"
                : "bg-red-50 text-red-700 border-red-200"
          }
        >
          {log.percentage}%
        </Badge>
      );
    }
    
    return null;
  };

  return (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold text-foreground">Presence logs</h3>
      
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className="text-left">Date</TableHead>
            <TableHead className="text-right">Status</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {logs.map((log) => (
            <TableRow key={log.id}>
              <TableCell className="font-medium">
                {log.label}
              </TableCell>
              <TableCell className="text-right">
                {getStatusBadge(log)}
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
}