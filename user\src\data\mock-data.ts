import { SavingsHolding } from '../types/schema';
import { SavingsCryptoCurrency } from '../types/enums';
import { TopTrendAsset } from './dashboard-data';

export const mockSavingsHoldings: SavingsHolding[] = [
  {
    currency: SavingsCryptoCurrency.BTC,
    name: 'Bitcoin',
    savedAmount: 0.05432,
    symbol: 'BTC'
  },
  {
    currency: SavingsCryptoCurrency.ETH,
    name: 'Ethereum',
    savedAmount: 1.2345,
    symbol: 'ETH'
  },
  {
    currency: SavingsCryptoCurrency.SOL,
    name: 'Sol<PERSON>',
    savedAmount: 25.67,
    symbol: 'SOL'
  },
  {
    currency: SavingsCryptoCurrency.DASH,
    name: 'Dash',
    savedAmount: 5.432,
    symbol: 'DASH'
  }
];

export const mockTopTrends: TopTrendAsset[] = [
  {
    rank: 1,
    asset: 'Bitcoin',
    symbol: 'BTC',
    collateralValue: '$2,345,678',
    volume24h: '$1,234,567',
    icon: '₿',
    color: '#F7931A'
  },
  {
    rank: 2,
    asset: 'Ethereum',
    symbol: 'ETH',
    collateralValue: '$1,876,543',
    volume24h: '$987,654',
    icon: '◆',
    color: '#627EEA'
  },
  {
    rank: 3,
    asset: 'Solana',
    symbol: 'SOL',
    collateralValue: '$654,321',
    volume24h: '$456,789',
    icon: 'S',
    color: '#9945FF'
  },
  {
    rank: 4,
    asset: 'Dash',
    symbol: 'DASH',
    collateralValue: '$321,987',
    volume24h: '$234,567',
    icon: 'Ð',
    color: '#008CE7'
  }
];