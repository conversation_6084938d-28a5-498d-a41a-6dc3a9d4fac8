// Import existing types
import { ChatMessage } from '@/types/schema';

// Props types for My Account page
export interface MyAccountProps {
  user: UserAccountData;
  presenceLogs: PresenceLogEntry[];
  chatMessages: ChatMessage[];
}

export interface UserAccountData {
  id: string;
  name: string;
  role: string;
  avatar: string;
  email: string;
  phone: {
    countryCode: string;
    number: string;
  };
  presenceScore: number;
  twoFactorEnabled: boolean;
}

export interface PresenceLogEntry {
  id: string;
  date: Date;
  label: string;
  percentage: number | null;
  timeLeft: number | null;
  status: 'active' | 'completed' | 'absent';
}

export interface MyAccountPageProps extends MyAccountProps {
  onUpdateEmail: (email: string) => void;
  onUpdatePhone: (countryCode: string, number: string) => void;
  onUpdatePassword: (password: string) => void;
  onToggle2FA: () => void;
}