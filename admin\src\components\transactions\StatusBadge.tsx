"use client";

import { Badge } from "@/components/ui/badge";
import { TransactionStatus } from "@/types/enums";

interface StatusBadgeProps {
  status: TransactionStatus;
}

export function StatusBadge({ status }: StatusBadgeProps) {
  const getStatusVariant = (status: TransactionStatus) => {
    switch (status) {
      case TransactionStatus.PROCESSING:
        return "secondary"; // Will be styled with orange
      case TransactionStatus.COMPLETED:
        return "default"; // Will be styled with green
      case TransactionStatus.FAILED:
        return "destructive"; // Will be styled with red
      default:
        return "secondary";
    }
  };

  const getStatusColor = (status: TransactionStatus) => {
    switch (status) {
      case TransactionStatus.PROCESSING:
        return "text-orange-600 bg-orange-100";
      case TransactionStatus.COMPLETED:
        return "text-green-600 bg-green-100";
      case TransactionStatus.FAILED:
        return "text-red-600 bg-red-100";
      default:
        return "text-gray-600 bg-gray-100";
    }
  };

  return (
    <Badge 
      variant={getStatusVariant(status)}
      className={`${getStatusColor(status)} border-0 font-medium`}
    >
      {status}
    </Badge>
  );
}