"use client";

import { Card } from "@/components/ui/card";
import { ChartContainer, ChartTooltip, ChartTooltipContent } from "@/components/ui/chart";
import { AreaChart, Area, XAxis, YAxis } from "recharts";

interface ReferralEarningData {
  month: string;
  earnings: number;
}

interface ReferralEarningsChartProps {
  data: ReferralEarningData[];
  chartConfig: any;
}

export function ReferralEarningsChart({ data, chartConfig }: ReferralEarningsChartProps) {
  return (
    <Card className="p-6 shadow-sm rounded-3xl">
      <h2 className="text-lg font-semibold mb-6 text-gray-900">Referral earning metrics</h2>
      <div className="max-h-[5rem]">
        <ChartContainer config={chartConfig} className="w-full h-full max-h-[5rem]">
          <AreaChart data={data}>
            <defs>
              <linearGradient id="earningsGradient" x1="0" y1="0" x2="0" y2="1">
                <stop offset="0%" stopColor="#0E4DDB" stopOpacity={1}/>
                <stop offset="10%" stopColor="#0E4DDB" stopOpacity={0.9}/>
                <stop offset="20%" stopColor="#0E4DDB" stopOpacity={0.8}/>
                <stop offset="100%" stopColor="#3b82f6" stopOpacity={0}/>
              </linearGradient>
            </defs>
            <XAxis dataKey="month" axisLine={false} tickLine={false} />
            {/* <YAxis axisLine={false} tickLine={false} /> */}
            <ChartTooltip content={<ChartTooltipContent />} />
            <Area
              dataKey="earnings"
              stroke="#3b82f6"
              fill="url(#earningsGradient)"
              strokeWidth={2}
            />
          </AreaChart>
        </ChartContainer>
      </div>
    </Card>
  );
}