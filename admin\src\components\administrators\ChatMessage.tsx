import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar";
import { ChatMessage as ChatMessageType } from "@/types/schema";
import { formatChatTime } from "@/utils/formatters";

interface ChatMessageProps {
  message: ChatMessageType;
}

export function ChatMessage({ message }: ChatMessageProps) {
  return (
    <div className="flex items-start gap-3 py-2">
      <Avatar className="h-8 w-8">
        <AvatarImage src={message.avatar} alt={message.sender} />
        <AvatarFallback>{message.sender.charAt(0)}</AvatarFallback>
      </Avatar>
      <div className="flex-1 min-w-0">
        <div className="flex items-baseline gap-2">
          <span className="text-sm font-medium text-foreground truncate">
            {message.sender}
          </span>
          <span className="text-xs text-muted-foreground">
            {formatChatTime(message.timestamp)}
          </span>
        </div>
        <p className="text-sm text-muted-foreground mt-1">
          {message.message}
        </p>
      </div>
    </div>
  );
}