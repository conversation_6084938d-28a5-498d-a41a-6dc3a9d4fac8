"use client";

import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { UserProfile as UserProfileType } from "@/types/schema";
import { Card } from "@/components/ui/card";

interface UserProfileProps {
  user: UserProfileType;
}

export function UserProfile({ user }: UserProfileProps) {
  return (
    <Card className="p-6">

      <div className="flex items-center gap-3">
        <Avatar className="h-10 w-10">
          <AvatarImage src={user.avatar} alt={user.name} />
          <AvatarFallback className="bg-blue-100 text-blue-600">
            {user.name.split(' ').map(n => n[0]).join('')}
          </AvatarFallback>
        </Avatar>
        <div className="flex flex-col">
          <div className="flex items-center gap-2">
            <span className="font-semibold text-gray-900">{user.name}</span>
            <span className="text-yellow-500 text-sm">★ {user.role}</span>
          </div>
          <span className="text-sm text-gray-500">{user.id}</span>
        </div>
      </div>
    </Card>
  );
}