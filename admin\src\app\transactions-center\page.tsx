"use client";

import { useState, useMemo } from "react";
import { Card } from "@/components/ui/card";
import { TransactionHeader } from "@/components/transactions/TransactionHeader";
import { TransactionTabs } from "@/components/transactions/TransactionTabs";
import { InsightsPanel } from "@/components/transactions/InsightsPanel";
import { ToolsPanel } from "@/components/transactions/ToolsPanel";
import { ServerStatusPanel } from "@/components/transactions/ServerStatusPanel";
import { mockQuery, mockRootProps } from "@/data/transactionsCenterMockData";
import { TransactionCategory } from "@/types/enums";

export default function TransactionsCenterPage() {
  const [selectedDate, setSelectedDate] = useState(mockRootProps.selectedDate);
  const [selectedTab, setSelectedTab] = useState<TransactionCategory>(mockRootProps.selectedTab);
  const [searchQuery, setSearchQuery] = useState(mockRootProps.searchQuery);

  // Filter transactions based on search query
  const filteredLoansData = useMemo(() => {
    if (!searchQuery) return mockQuery.loansTransactions;
    return mockQuery.loansTransactions.filter(transaction =>
      transaction.id.toLowerCase().includes(searchQuery.toLowerCase())
    );
  }, [searchQuery]);

  const filteredSendReceiveData = useMemo(() => {
    if (!searchQuery) return mockQuery.sendReceiveTransactions;
    return mockQuery.sendReceiveTransactions.filter(transaction =>
      transaction.id.toLowerCase().includes(searchQuery.toLowerCase())
    );
  }, [searchQuery]);

  const filteredExchangesData = useMemo(() => {
    if (!searchQuery) return mockQuery.exchangesTransactions;
    return mockQuery.exchangesTransactions.filter(transaction =>
      transaction.id.toLowerCase().includes(searchQuery.toLowerCase())
    );
  }, [searchQuery]);

  const filteredSavingsData = useMemo(() => {
    if (!searchQuery) return mockQuery.savingsTransactions;
    return mockQuery.savingsTransactions.filter(transaction =>
      transaction.id.toLowerCase().includes(searchQuery.toLowerCase())
    );
  }, [searchQuery]);

  const filteredReferralsData = useMemo(() => {
    if (!searchQuery) return mockQuery.referralsTransactions;
    return mockQuery.referralsTransactions.filter(transaction =>
      transaction.id.toLowerCase().includes(searchQuery.toLowerCase())
    );
  }, [searchQuery]);

  const handleExport = () => {
    console.log(`Exporting ${selectedTab} transactions for ${selectedDate}`);
    // Export functionality would be implemented here
  };

  const handleToolClick = (tool: string) => {
    console.log(`Opening ${tool}`);
    // Tool functionality would be implemented here
  };

  return (
    <div className="w-full min-h-screen p-6 bg-background">
      <div className="h-full grid grid-cols-1 gap-6 xl:grid-cols-[6fr_4fr]">
        {/* Main Content */}
        <div className="w-full min-h-full flex flex-col gap-6">
          <Card className="p-6">
            <TransactionHeader
              selectedDate={selectedDate}
              searchQuery={searchQuery}
              onDateChange={setSelectedDate}
              onSearchChange={setSearchQuery}
              onExport={handleExport}
            />
            
            <TransactionTabs
              selectedTab={selectedTab}
              onTabChange={setSelectedTab}
              loansData={filteredLoansData}
              sendReceiveData={filteredSendReceiveData}
              exchangesData={filteredExchangesData}
              savingsData={filteredSavingsData}
              referralsData={filteredReferralsData}
            />
          </Card>
        </div>

        {/* Right Sidebar */}
        <div className="w-full space-y-6">
          <InsightsPanel insights={mockQuery.insights} />
          <ToolsPanel onToolClick={handleToolClick} />
          <ServerStatusPanel serverStatus={mockQuery.serverStatus} />
        </div>
      </div>
    </div>
  );
}