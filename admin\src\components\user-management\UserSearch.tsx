"use client";

import { Input } from "@/components/ui/input";
import { Search } from "lucide-react";
import { useState } from "react";

interface UserSearchProps {
  onSearch: (query: string) => void;
  placeholder?: string;
}

export function UserSearch({ onSearch, placeholder = "Find by email" }: UserSearchProps) {
  const [searchQuery, setSearchQuery] = useState("");

  const handleSearch = (value: string) => {
    setSearchQuery(value);
    onSearch(value);
  };

  return (
    <div className="relative w-full max-w-md">
      <Search 
        size={16} 
        className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground" 
      />
      <Input
        type="email"
        placeholder={placeholder}
        value={searchQuery}
        onChange={(e) => handleSearch(e.target.value)}
        className="pl-10 pr-4 py-2 w-full"
      />
    </div>
  );
}