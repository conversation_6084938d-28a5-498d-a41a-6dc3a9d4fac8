"use client";

import React, { useState, useMemo } from 'react';
import { useViewportType } from "@/hooks/use-viewport";
import { LoanFilters } from '@/components/loan/LoanFilters';
import { LoanCard } from '@/components/loan/LoanCard';
import { PortfolioInsights } from '@/components/loan/PortfolioInsights';
import { mockRootProps } from '@/data/loanDashboardMockData';
import { LoanHealth } from '@/types/enums';

export default function LoanPage() {
  const { isMobile, isTablet } = useViewportType();
  const [searchTerm, setSearchTerm] = useState('');
  const [healthFilter, setHealthFilter] = useState('all');

  // Filter loans based on search term and health status
  const filteredLoans = useMemo(() => {
    return mockRootProps.loans.filter(loan => {
      const matchesSearch = loan.collateralCurrency.toLowerCase().includes(searchTerm.toLowerCase()) ||
        loan.ratePair.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesHealth = healthFilter === 'all' || loan.health === healthFilter;
      return matchesSearch && matchesHealth;
    });
  }, [searchTerm, healthFilter]);

  const handleGetLoan = () => {
    console.log('Get loan clicked');
  };

  const handleDownloadCSV = () => {
    console.log('Download CSV clicked');
  };

  // Mobile and Tablet Layout (< 1200px)
  if (isMobile || isTablet) {
    return (
      <div className="w-full h-screen p-2 sm:p-4 bg-gray-50 overflow-hidden">
        <div className="w-full h-full flex flex-col gap-2 sm:gap-4 overflow-hidden">
          <div className="flex-shrink-0">
            <LoanFilters
              searchTerm={searchTerm}
              onSearchChange={setSearchTerm}
              healthFilter={healthFilter}
              onHealthFilterChange={setHealthFilter}
              onGetLoan={handleGetLoan}
              onDownloadCSV={handleDownloadCSV}
            />
          </div>

          <div className="flex-1 min-h-0 max-h-full overflow-y-auto space-y-2 sm:space-y-4">
            {/* Header Row */}
            <div className="flex items-center justify-between px-4 py-2 text-sm font-medium text-muted-foreground">
              <div className="w-48">Collateral</div>
              <div className="flex-1 text-center ml-8">Current rate</div>
              <div className="text-right">Margin call</div>
            </div>

            {filteredLoans.map((loan) => (
              <LoanCard key={loan.id} loan={loan} />
            ))}
          </div>

          <div className="flex-shrink-0">
            <PortfolioInsights portfolioInsights={mockRootProps.portfolioInsights} />
          </div>
        </div>
      </div>
    );
  }

  // Desktop Layout (≥ 1200px)
  return (
    <div className="w-full h-screen p-16 bg-gray-300/50 overflow-hidden">
      <div className="w-full h-full grid grid-cols-1 gap-4 lg:gap-6 xl:grid-cols-[6fr_4fr] overflow-hidden">
        <div className="min-h-0 max-h-full flex flex-col gap-4 lg:gap-6 overflow-hidden">
          <div className="flex-shrink-0">
            <LoanFilters
              searchTerm={searchTerm}
              onSearchChange={setSearchTerm}
              healthFilter={healthFilter}
              onHealthFilterChange={setHealthFilter}
              onGetLoan={handleGetLoan}
              onDownloadCSV={handleDownloadCSV}
            />
          </div>

          <div className="flex-1 min-h-0 max-h-full overflow-y-auto">
            <div className="space-y-3 lg:space-y-4">
              {/* Header Row */}
              <div className="flex items-center justify-between px-4 py-2 text-sm font-medium text-muted-foreground">
                <div className="w-[60%]">Collateral</div>
                <div className='w-[40%] flex'>
                  <div className="flex-1 text-left">Current rate</div>
                  <div className="flex-1 text-left">Margin call</div>
                </div>
              </div>

              {filteredLoans.map((loan) => (
                <LoanCard key={loan.id} loan={loan} />
              ))}
            </div>
          </div>
        </div>

        <div className="min-h-0 max-h-full overflow-hidden">
          <PortfolioInsights portfolioInsights={mockRootProps.portfolioInsights} />
        </div>
      </div>
    </div>
  );
}