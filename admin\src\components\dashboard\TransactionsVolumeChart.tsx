"use client";

import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Legend, ResponsiveContainer } from "recharts";
import { ChartContainer, ChartTooltip, ChartTooltipContent, ChartLegend, ChartLegendContent } from "@/components/ui/chart";
import { TransactionDataPoint } from "@/types/schema";

interface TransactionsVolumeChartProps {
  data: TransactionDataPoint[];
}

const chartConfig = {
  Loans: {
    label: "Loans ($1,200)",
    color: "#8B5CF6",
  },
  Sending: {
    label: "Sending ($741)",
    color: "#F59E0B",
  },
  Buying: {
    label: "Buying ($374)",
    color: "#EF4444",
  },
  Savings: {
    label: "Savings ($987)",
    color: "#10B981",
  },
  Receives: {
    label: "Receives ($729)",
    color: "#EC4899",
  },
  Sellings: {
    label: "Sellings ($278)",
    color: "#06B6D4",
  },
};

export function TransactionsVolumeChart({ data }: TransactionsVolumeChartProps) {
  return (
    <div className="w-full h-full">
      <ChartContainer config={chartConfig} className="w-full h-full">
        <LineChart data={data} margin={{ top: 20, right: 30, left: 20, bottom: 20 }}>
          <XAxis 
            dataKey="time" 
            axisLine={false}
            tickLine={false}
            tick={{ fontSize: 12, fill: '#6B7280' }}
          />
          <YAxis hide />
          <ChartTooltip content={<ChartTooltipContent />} />
          <ChartLegend content={<ChartLegendContent />} />
          <Line
            type="monotone"
            dataKey="Loans"
            stroke={chartConfig.Loans.color}
            strokeWidth={2}
            dot={false}
          />
          <Line
            type="monotone"
            dataKey="Sending"
            stroke={chartConfig.Sending.color}
            strokeWidth={2}
            dot={false}
          />
          <Line
            type="monotone"
            dataKey="Buying"
            stroke={chartConfig.Buying.color}
            strokeWidth={2}
            dot={false}
          />
          <Line
            type="monotone"
            dataKey="Savings"
            stroke={chartConfig.Savings.color}
            strokeWidth={2}
            dot={false}
          />
          <Line
            type="monotone"
            dataKey="Receives"
            stroke={chartConfig.Receives.color}
            strokeWidth={2}
            dot={false}
          />
          <Line
            type="monotone"
            dataKey="Sellings"
            stroke={chartConfig.Sellings.color}
            strokeWidth={2}
            dot={false}
          />
        </LineChart>
      </ChartContainer>
    </div>
  );
}